#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器
统一运行所有测试模块并生成测试报告
"""

import sys
import os
import subprocess
import time
from datetime import datetime


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        """初始化测试运行器"""
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # 测试模块列表
        self.test_modules = [
            {
                'name': '结构测试',
                'description': '测试项目结构和模块导入',
                'command': 'python tests/test_comprehensive.py',
                'required': True
            },
            {
                'name': '图像预处理测试',
                'description': '测试图像预处理模块',
                'command': 'python tests/test_image_processor_structure.py',
                'required': False
            },
            {
                'name': '边框检测测试',
                'description': '测试边框检测算法',
                'command': 'python tests/test_border_detector_structure.py',
                'required': False
            },
            {
                'name': '坐标计算测试',
                'description': '测试坐标计算模块',
                'command': 'python tests/test_coordinate_calculator_structure.py',
                'required': False
            },
            {
                'name': '可视化测试',
                'description': '测试结果可视化模块',
                'command': 'python tests/test_visualizer_structure.py',
                'required': False
            },
            {
                'name': '系统集成测试',
                'description': '测试系统整体集成',
                'command': 'python tests/test_integration.py',
                'required': False
            },
            {
                'name': '性能测试',
                'description': '测试系统性能表现',
                'command': 'python tests/test_performance.py',
                'required': False
            }
        ]
    
    def run_single_test(self, test_module):
        """
        运行单个测试模块
        Args:
            test_module: 测试模块信息
        Returns:
            dict: 测试结果
        """
        print(f"\n--- {test_module['name']} ---")
        print(f"描述: {test_module['description']}")
        print(f"命令: {test_module['command']}")
        
        result = {
            'name': test_module['name'],
            'command': test_module['command'],
            'start_time': time.time(),
            'success': False,
            'return_code': -1,
            'output': '',
            'error': '',
            'duration': 0
        }
        
        try:
            # 运行测试命令
            process = subprocess.run(
                test_module['command'].split(),
                capture_output=True,
                text=True,
                timeout=120  # 2分钟超时
            )
            
            result['return_code'] = process.returncode
            result['output'] = process.stdout
            result['error'] = process.stderr
            result['success'] = process.returncode == 0
            
            if result['success']:
                print("✅ 测试通过")
            else:
                print("❌ 测试失败")
                if process.stderr:
                    print(f"错误信息: {process.stderr[:200]}...")
            
        except subprocess.TimeoutExpired:
            print("⏰ 测试超时")
            result['error'] = "测试执行超时"
        except FileNotFoundError:
            print("📁 测试文件不存在")
            result['error'] = "测试文件不存在"
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            result['error'] = str(e)
        
        result['end_time'] = time.time()
        result['duration'] = result['end_time'] - result['start_time']
        
        return result
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=== 开始运行所有测试 ===")
        self.start_time = time.time()
        
        # 检查测试文件是否存在
        missing_files = []
        for module in self.test_modules:
            test_file = module['command'].split()[1]  # 提取文件路径
            if not os.path.exists(test_file):
                missing_files.append(test_file)
        
        if missing_files:
            print("⚠ 以下测试文件不存在:")
            for file in missing_files:
                print(f"  - {file}")
        
        # 运行每个测试模块
        for module in self.test_modules:
            test_file = module['command'].split()[1]
            if os.path.exists(test_file):
                result = self.run_single_test(module)
                self.test_results[module['name']] = result
            else:
                print(f"\n--- {module['name']} ---")
                print("⚠ 测试文件不存在，跳过")
                self.test_results[module['name']] = {
                    'name': module['name'],
                    'success': False,
                    'error': '测试文件不存在',
                    'duration': 0
                }
        
        self.end_time = time.time()
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("测试报告")
        print("="*60)
        
        # 基本统计
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r['success'])
        failed_tests = total_tests - passed_tests
        total_duration = self.end_time - self.start_time if self.start_time else 0
        
        print(f"测试时间: {datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {total_duration:.2f}秒")
        print(f"测试总数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        # 详细结果
        print(f"\n详细结果:")
        print("-" * 60)
        
        for name, result in self.test_results.items():
            status = "✅ 通过" if result['success'] else "❌ 失败"
            duration = result.get('duration', 0)
            print(f"{name:<20} {status:<8} {duration:>6.2f}s")
            
            if not result['success'] and result.get('error'):
                print(f"  错误: {result['error'][:100]}...")
        
        # 保存报告到文件
        self.save_report_to_file()
        
        # 总结
        print(f"\n" + "="*60)
        if failed_tests == 0:
            print("🎉 所有测试都通过了！")
            print("系统已准备就绪，可以正常使用。")
        else:
            print(f"⚠ {failed_tests} 个测试失败")
            print("请检查失败的测试模块并修复问题。")
            
            # 检查关键测试
            critical_failures = []
            for module in self.test_modules:
                if module['required'] and not self.test_results[module['name']]['success']:
                    critical_failures.append(module['name'])
            
            if critical_failures:
                print(f"❌ 关键测试失败: {', '.join(critical_failures)}")
                print("这些是必需的测试，请优先修复。")
    
    def save_report_to_file(self):
        """保存报告到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"test_report_{timestamp}.txt"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("边框检测系统测试报告\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"测试时间: {datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总耗时: {self.end_time - self.start_time:.2f}秒\n\n")
                
                # 统计信息
                total = len(self.test_results)
                passed = sum(1 for r in self.test_results.values() if r['success'])
                f.write(f"测试统计:\n")
                f.write(f"  总数: {total}\n")
                f.write(f"  通过: {passed}\n")
                f.write(f"  失败: {total - passed}\n")
                f.write(f"  成功率: {passed/total*100:.1f}%\n\n")
                
                # 详细结果
                f.write("详细结果:\n")
                f.write("-" * 50 + "\n")
                
                for name, result in self.test_results.items():
                    status = "通过" if result['success'] else "失败"
                    duration = result.get('duration', 0)
                    f.write(f"{name}: {status} ({duration:.2f}s)\n")
                    
                    if not result['success']:
                        f.write(f"  错误: {result.get('error', '未知错误')}\n")
                        if result.get('output'):
                            f.write(f"  输出: {result['output'][:500]}...\n")
                    f.write("\n")
            
            print(f"✓ 测试报告已保存: {report_file}")
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
    
    def run_quick_test(self):
        """运行快速测试（仅必需的测试）"""
        print("=== 运行快速测试 ===")
        
        quick_tests = [m for m in self.test_modules if m['required']]
        
        if not quick_tests:
            print("没有标记为必需的测试")
            return
        
        self.start_time = time.time()
        
        for module in quick_tests:
            result = self.run_single_test(module)
            self.test_results[module['name']] = result
        
        self.end_time = time.time()
        self.generate_report()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='边框检测系统测试运行器')
    parser.add_argument('--quick', '-q', action='store_true', help='仅运行快速测试')
    parser.add_argument('--generate-images', '-g', action='store_true', help='生成测试图像')
    
    args = parser.parse_args()
    
    print("=== 边框检测系统测试运行器 ===")
    
    # 生成测试图像
    if args.generate_images:
        print("\n--- 生成测试图像 ---")
        try:
            subprocess.run(['python', 'tests/generate_test_images.py'], check=True)
            print("✓ 测试图像生成完成")
        except subprocess.CalledProcessError:
            print("❌ 测试图像生成失败")
        except FileNotFoundError:
            print("❌ 测试图像生成器不存在")
    
    # 运行测试
    runner = TestRunner()
    
    if args.quick:
        runner.run_quick_test()
    else:
        runner.run_all_tests()
    
    # 返回退出码
    failed_count = sum(1 for r in runner.test_results.values() if not r['success'])
    return 0 if failed_count == 0 else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
