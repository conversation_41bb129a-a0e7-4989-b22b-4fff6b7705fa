# OpenCV边框检测系统

用于识别A4纸上2cm宽黑色边框线的内外边界的计算机视觉系统。

## 🎥 摄像头实时检测（推荐使用）

### 快速开始
```bash
# 1. 测试摄像头功能
python test_camera.py

# 2. 运行实时边框检测
python camera_border_detection.py

# 3. 使用指定摄像头和DPI
python camera_border_detection.py --camera 1 --dpi 150
```

### 使用说明
1. **准备测试图像**：运行 `python test_camera.py` 会生成 `test_border_image.jpg`
2. **打印测试图像**：将生成的图像打印在A4纸上
3. **实时检测**：运行 `python camera_border_detection.py`
4. **操作控制**：
   - 按 `q` 退出检测
   - 按 `s` 保存当前帧和检测信息
   - 按 `d` 切换调试模式

### 检测效果
- **绿色线条**：外边框（完整的A4纸边界）
- **红色线条**：内边框（去除2cm边框后的内容区域）
- **实时信息**：显示FPS、检测状态、边框宽度等

## 📁 传统文件处理模式

### 单张图像处理
```bash
# 简单检测
python main.py --image sample.jpg

# 详细检测（包含调试信息）
python main.py --image sample.jpg --detailed --output results/

# 指定DPI
python main.py --image sample.jpg --dpi 150
```

### 批量处理
```bash
# 批量处理目录中的所有图像
python main.py --batch images/ --output results/

# 不保存结果图像
python main.py --batch images/ --no-save
```

### 系统管理
```bash
# 显示系统信息
python main.py --info

# 运行系统测试
python main.py --test
```

## 🛠️ 安装与配置

### 系统要求
- Python 3.7+
- OpenCV 4.0+
- NumPy 1.19+
- 摄像头设备（用于实时检测）

### 安装依赖
```bash
# 如果系统中没有OpenCV，可以安装
pip install opencv-python numpy matplotlib

# 或使用项目提供的requirements.txt
pip install -r requirements.txt
```

### 项目结构
```
├── camera_border_detection.py  # 摄像头实时检测（主要功能）
├── test_camera.py             # 摄像头功能测试
├── main.py                    # 传统文件处理入口
├── config.py                  # 系统配置参数
├── src/                       # 核心模块
│   ├── image_processor.py     # 图像预处理
│   ├── border_detector.py     # 边框检测算法
│   ├── coordinate_calculator.py # 坐标计算
│   ├── visualizer.py          # 结果可视化
│   └── border_detection_system.py # 系统集成
├── tests/                     # 测试模块
├── examples/                  # 使用示例
└── README.md                  # 说明文档
```

## ⚙️ 配置参数

### 主要参数（config.py）
```python
# 像素密度设置
PIXELS_PER_CM = 28.35  # 72DPI对应的像素密度
DPI_72 = 28.35         # 72DPI
DPI_96 = 37.8          # 96DPI  
DPI_150 = 59.06        # 150DPI
DPI_300 = 118.11       # 300DPI

# 边框参数
BORDER_WIDTH_CM = 2    # 边框宽度(厘米)

# 可视化颜色
OUTER_BORDER_COLOR = (0, 255, 0)  # 外边界颜色(绿色)
INNER_BORDER_COLOR = (0, 0, 255)  # 内边界颜色(红色)
```

### DPI设置说明
- **72 DPI**：标准屏幕显示密度
- **96 DPI**：Windows标准显示密度
- **150 DPI**：高清打印密度
- **300 DPI**：专业打印密度

## 🔧 故障排除

### 依赖安装问题
如果遇到NumPy或OpenCV导入错误：

```bash
# 运行自动安装脚本
./install_dependencies.sh

# 或手动安装
sudo apt update
sudo apt install python3-opencv python3-numpy libopenblas-dev

# 对于Jetson设备
sudo apt install nvidia-opencv
```

### 摄像头问题
```bash
# 测试摄像头是否可用
python3 basic_camera_test.py

# 尝试不同的摄像头ID
python3 camera_border_detection.py --camera 1
python3 camera_border_detection.py --camera 2
```

### 检测问题
1. **检测不到边框**：
   - 确保A4纸边框清晰、对比度高
   - 调整光照条件，避免反光
   - 尝试调整DPI参数

2. **检测不准确**：
   - 检查纸张是否平整
   - 确保摄像头焦距清晰
   - 调整config.py中的检测参数

3. **性能问题**：
   - 降低摄像头分辨率
   - 关闭调试模式
   - 检查系统资源使用情况

### 环境问题
如果无法安装依赖，可以运行演示版本：
```bash
# 功能演示（不需要OpenCV）
python3 demo_border_detection.py

# 查看系统架构和测试结果
python3 run_all_tests.py --quick
```

## 📖 使用示例

### 摄像头检测示例
```python
from camera_border_detection import CameraBorderDetector

# 创建检测器
detector = CameraBorderDetector(camera_id=0, dpi=72)

# 运行实时检测
detector.run()
```

### 文件处理示例
```python
from src.border_detection_system import BorderDetectionSystem

# 创建系统
system = BorderDetectionSystem(dpi=72)

# 检测单张图像
outer_rect, inner_rect, result_image = system.detect_boundaries("sample.jpg")

# 详细检测
result = system.detect_boundaries_detailed("sample.jpg", save_debug=True)
```

## 📝 更新日志

### v1.0.0
- ✅ 完整的边框检测算法
- ✅ 摄像头实时检测功能
- ✅ 多种DPI支持
- ✅ 批量处理功能
- ✅ 命令行接口
- ✅ 详细的调试信息

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目采用MIT许可证。
