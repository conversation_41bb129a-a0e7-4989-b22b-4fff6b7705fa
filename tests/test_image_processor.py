#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预处理模块测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.image_processor import ImageProcessor
except ImportError as e:
    print(f"导入失败: {e}")
    print("跳过OpenCV相关测试")
    sys.exit(0)


def create_test_image():
    """创建测试用的A4纸黑色边框图像"""
    # 创建白色背景 (A4比例: 210x297mm, 使用800x600像素)
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 添加2cm宽的黑色边框 (假设28.35像素/cm)
    border_width = int(2 * 28.35)  # 约57像素
    
    # 绘制边框
    image[:border_width, :] = [0, 0, 0]  # 上边框
    image[-border_width:, :] = [0, 0, 0]  # 下边框
    image[:, :border_width] = [0, 0, 0]  # 左边框
    image[:, -border_width:] = [0, 0, 0]  # 右边框
    
    return image


def add_noise_and_lighting(image):
    """添加噪声和光照不均效果"""
    noisy_image = image.copy()
    
    # 添加高斯噪声
    noise = np.random.normal(0, 15, image.shape).astype(np.uint8)
    noisy_image = cv2.add(noisy_image, noise)
    
    # 模拟光照不均（添加渐变）
    h, w = image.shape[:2]
    gradient = np.zeros((h, w), dtype=np.float32)
    for i in range(h):
        for j in range(w):
            # 创建从左上到右下的亮度渐变
            gradient[i, j] = 0.7 + 0.3 * (i + j) / (h + w)
    
    # 应用渐变到每个通道
    for c in range(3):
        noisy_image[:, :, c] = np.clip(
            noisy_image[:, :, c].astype(np.float32) * gradient, 0, 255
        ).astype(np.uint8)
    
    return noisy_image


def test_basic_functionality():
    """测试基本功能"""
    print("1. 测试基本预处理功能...")
    
    # 创建测试图像
    test_image = create_test_image()
    
    # 创建处理器
    processor = ImageProcessor()
    
    try:
        # 执行预处理
        result = processor.preprocess(test_image)
        
        # 验证结果
        assert result is not None, "预处理结果为空"
        assert len(result.shape) == 2, "结果应该是灰度图像"
        assert result.dtype == np.uint8, "结果数据类型应该是uint8"
        
        # 检查二值化效果
        unique_values = np.unique(result)
        assert len(unique_values) <= 2, f"二值化图像应该最多包含2个值，实际包含: {unique_values}"
        
        print("✓ 基本预处理功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本预处理功能测试失败: {e}")
        return False


def test_noise_handling():
    """测试噪声处理能力"""
    print("2. 测试噪声处理能力...")
    
    try:
        # 创建带噪声的测试图像
        clean_image = create_test_image()
        noisy_image = add_noise_and_lighting(clean_image)
        
        processor = ImageProcessor()
        
        # 处理干净图像和噪声图像
        clean_result = processor.preprocess(clean_image)
        noisy_result = processor.preprocess(noisy_image)
        
        # 验证两个结果的相似性（噪声应该被有效抑制）
        assert clean_result.shape == noisy_result.shape, "处理结果尺寸应该相同"
        
        # 计算差异像素比例
        diff_pixels = np.sum(clean_result != noisy_result)
        total_pixels = clean_result.size
        diff_ratio = diff_pixels / total_pixels
        
        # 差异应该在合理范围内（小于20%）
        assert diff_ratio < 0.2, f"噪声处理后差异过大: {diff_ratio:.2%}"
        
        print(f"✓ 噪声处理测试通过，差异比例: {diff_ratio:.2%}")
        return True
        
    except Exception as e:
        print(f"❌ 噪声处理测试失败: {e}")
        return False


def test_edge_cases():
    """测试边界情况"""
    print("3. 测试边界情况...")
    
    processor = ImageProcessor()
    
    try:
        # 测试空图像
        try:
            processor.preprocess(None)
            assert False, "应该抛出异常"
        except ValueError:
            print("✓ 空图像异常处理正确")
        
        # 测试极小图像
        tiny_image = np.ones((10, 10, 3), dtype=np.uint8) * 128
        result = processor.preprocess(tiny_image)
        assert result is not None, "极小图像处理失败"
        print("✓ 极小图像处理正确")
        
        # 测试灰度图像输入
        gray_image = np.ones((100, 100), dtype=np.uint8) * 128
        result = processor.preprocess(gray_image)
        assert result is not None, "灰度图像处理失败"
        print("✓ 灰度图像输入处理正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        return False


def test_debug_functionality():
    """测试调试功能"""
    print("4. 测试调试功能...")
    
    try:
        test_image = create_test_image()
        processor = ImageProcessor()
        
        # 测试调试显示功能
        debug_results = processor.debug_show_steps(test_image)
        
        # 验证调试结果
        expected_keys = ['original', 'gray', 'blurred', 'binary', 'processed']
        for key in expected_keys:
            assert key in debug_results, f"调试结果缺少 {key}"
            assert debug_results[key] is not None, f"{key} 结果为空"
        
        print("✓ 调试功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 调试功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=== 图像预处理模块测试 ===")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_basic_functionality())
    test_results.append(test_noise_handling())
    test_results.append(test_edge_cases())
    test_results.append(test_debug_functionality())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("✅ 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
