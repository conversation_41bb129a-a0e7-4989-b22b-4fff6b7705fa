#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
边框检测核心算法模块
实现基于轮廓检测的边框识别算法，使用Canny边缘检测和轮廓分析
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from config import *
except ImportError as e:
    print(f"导入依赖失败: {e}")
    print("请确保已安装OpenCV和NumPy")
    sys.exit(1)


class BorderDetector:
    """边框检测类，专门用于检测A4纸的黑色边框轮廓"""
    
    def __init__(self):
        """初始化边框检测器"""
        self.canny_low = CANNY_LOW_THRESHOLD  # Canny低阈值
        self.canny_high = CANNY_HIGH_THRESHOLD  # Canny高阈值
        self.min_area = MIN_CONTOUR_AREA  # 最小轮廓面积
        self.approx_epsilon = CONTOUR_APPROX_EPSILON  # 轮廓近似精度
        self.max_vertices = MAX_CONTOUR_VERTICES  # 最大顶点数
        self.min_aspect_ratio = MIN_ASPECT_RATIO  # 最小宽高比
        self.max_aspect_ratio = MAX_ASPECT_RATIO  # 最大宽高比
    
    def detect_border(self, binary_image):
        """
        检测边框轮廓的主要方法
        Args:
            binary_image: 预处理后的二值化图像
        Returns:
            border_contour: 检测到的边框轮廓，如果未找到则返回None
        """
        if binary_image is None:
            raise ValueError("输入的二值化图像为空")
        
        if len(binary_image.shape) != 2:
            raise ValueError("输入图像必须是灰度图像")
        
        # 1. Canny边缘检测
        edges = self._canny_edge_detection(binary_image)
        
        # 2. 查找轮廓
        contours = self._find_contours(edges)
        
        # 3. 筛选矩形边框轮廓
        border_contour = self.filter_rectangular_contour(contours)
        
        return border_contour
    
    def _canny_edge_detection(self, binary_image):
        """
        执行Canny边缘检测
        Args:
            binary_image: 二值化图像
        Returns:
            edges: 边缘图像
        """
        edges = cv2.Canny(binary_image, self.canny_low, self.canny_high)
        return edges
    
    def _find_contours(self, edges):
        """
        查找轮廓
        Args:
            edges: 边缘图像
        Returns:
            contours: 轮廓列表
        """
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        return contours
    
    def filter_rectangular_contour(self, contours):
        """
        筛选矩形边框轮廓
        Args:
            contours: 轮廓列表
        Returns:
            best_contour: 最佳的矩形轮廓，如果未找到则返回None
        """
        if not contours:
            return None
        
        candidates = []
        
        for contour in contours:
            # 1. 面积筛选
            area = cv2.contourArea(contour)
            if area < self.min_area:
                continue
            
            # 2. 轮廓近似，获得多边形顶点
            perimeter = cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, self.approx_epsilon * perimeter, True)
            
            # 3. 顶点数筛选（矩形应该有4个顶点）
            if len(approx) != 4:
                continue
            
            # 4. 宽高比筛选
            if not self._check_aspect_ratio(approx):
                continue
            
            # 5. 凸性检查
            if not cv2.isContourConvex(approx):
                continue
            
            # 6. 计算轮廓质量分数
            score = self._calculate_contour_score(contour, approx, area)
            
            candidates.append((contour, approx, area, score))
        
        # 选择最佳候选轮廓
        if candidates:
            # 按分数排序，选择最高分的轮廓
            candidates.sort(key=lambda x: x[3], reverse=True)
            best_contour = candidates[0][0]
            return best_contour
        
        return None
    
    def _check_aspect_ratio(self, approx_contour):
        """
        检查轮廓的宽高比是否合理
        Args:
            approx_contour: 近似轮廓
        Returns:
            bool: 宽高比是否合理
        """
        # 获取边界矩形
        x, y, w, h = cv2.boundingRect(approx_contour)
        
        # 计算宽高比
        aspect_ratio = w / h if h > 0 else 0
        
        # 检查宽高比是否在合理范围内
        return self.min_aspect_ratio <= aspect_ratio <= self.max_aspect_ratio
    
    def _calculate_contour_score(self, contour, approx_contour, area):
        """
        计算轮廓质量分数
        Args:
            contour: 原始轮廓
            approx_contour: 近似轮廓
            area: 轮廓面积
        Returns:
            score: 质量分数（越高越好）
        """
        score = 0
        
        # 1. 面积分数（面积越大分数越高）
        area_score = min(area / 10000, 100)  # 归一化到0-100
        score += area_score
        
        # 2. 矩形度分数（越接近矩形分数越高）
        rect_area = cv2.contourArea(approx_contour)
        if rect_area > 0:
            rectangularity = area / rect_area
            rect_score = rectangularity * 50  # 最高50分
            score += rect_score
        
        # 3. 周长比分数（近似轮廓与原轮廓周长比）
        original_perimeter = cv2.arcLength(contour, True)
        approx_perimeter = cv2.arcLength(approx_contour, True)
        if original_perimeter > 0:
            perimeter_ratio = approx_perimeter / original_perimeter
            perimeter_score = (1 - abs(1 - perimeter_ratio)) * 30  # 最高30分
            score += perimeter_score
        
        # 4. 位置分数（边框通常在图像边缘）
        image_center_bonus = self._calculate_position_score(approx_contour)
        score += image_center_bonus
        
        return score
    
    def _calculate_position_score(self, approx_contour):
        """
        计算位置分数（边框轮廓通常靠近图像边缘）
        Args:
            approx_contour: 近似轮廓
        Returns:
            position_score: 位置分数
        """
        # 获取轮廓的边界矩形
        x, y, w, h = cv2.boundingRect(approx_contour)
        
        # 假设图像尺寸（这里需要从外部传入，暂时使用默认值）
        # 在实际使用中，可以通过修改方法签名来传入图像尺寸
        img_width, img_height = 800, 600  # 默认尺寸
        
        # 计算轮廓到图像边缘的距离
        edge_distance = min(x, y, img_width - (x + w), img_height - (y + h))
        
        # 边缘距离越小，分数越高（最高20分）
        position_score = max(0, 20 - edge_distance / 10)
        
        return position_score
    
    def get_contour_info(self, contour):
        """
        获取轮廓的详细信息
        Args:
            contour: 轮廓
        Returns:
            info: 轮廓信息字典
        """
        if contour is None:
            return None
        
        # 基本信息
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        
        # 边界矩形
        x, y, w, h = cv2.boundingRect(contour)
        
        # 近似轮廓
        approx = cv2.approxPolyDP(contour, self.approx_epsilon * perimeter, True)
        
        # 质量分数
        score = self._calculate_contour_score(contour, approx, area)
        
        info = {
            'area': area,
            'perimeter': perimeter,
            'bounding_rect': (x, y, w, h),
            'vertices_count': len(approx),
            'vertices': approx.reshape(-1, 2).tolist(),
            'aspect_ratio': w / h if h > 0 else 0,
            'quality_score': score,
            'is_convex': cv2.isContourConvex(approx)
        }
        
        return info
    
    def debug_show_detection_steps(self, binary_image, save_path=None):
        """
        调试功能：显示边框检测的各个步骤
        Args:
            binary_image: 输入的二值化图像
            save_path: 保存路径（可选）
        Returns:
            debug_results: 调试结果字典
        """
        if binary_image is None:
            print("输入图像为空，无法进行调试显示")
            return None
        
        # 各步骤处理
        edges = self._canny_edge_detection(binary_image)
        contours = self._find_contours(edges)
        border_contour = self.filter_rectangular_contour(contours)
        
        # 创建可视化图像
        vis_edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
        vis_contours = cv2.cvtColor(binary_image, cv2.COLOR_GRAY2BGR)
        vis_result = cv2.cvtColor(binary_image, cv2.COLOR_GRAY2BGR)
        
        # 绘制所有轮廓
        if contours:
            cv2.drawContours(vis_contours, contours, -1, (0, 255, 0), 2)
        
        # 绘制检测到的边框
        if border_contour is not None:
            cv2.drawContours(vis_result, [border_contour], -1, (0, 0, 255), 3)
        
        # 保存调试图像
        if save_path:
            cv2.imwrite(f"{save_path}_edges.jpg", edges)
            cv2.imwrite(f"{save_path}_contours.jpg", vis_contours)
            cv2.imwrite(f"{save_path}_result.jpg", vis_result)
            print(f"调试图像已保存到: {save_path}_*.jpg")
        
        debug_results = {
            'original': binary_image,
            'edges': edges,
            'all_contours': contours,
            'border_contour': border_contour,
            'vis_edges': vis_edges,
            'vis_contours': vis_contours,
            'vis_result': vis_result,
            'contour_count': len(contours) if contours else 0,
            'border_found': border_contour is not None
        }
        
        return debug_results


def test_border_detector():
    """测试边框检测器"""
    print("=== 边框检测模块测试 ===")
    
    # 创建测试二值化图像（模拟预处理后的结果）
    test_image = np.zeros((600, 800), dtype=np.uint8)  # 黑色背景
    
    # 添加白色边框（模拟预处理后的边框）
    border_width = 56
    cv2.rectangle(test_image, (0, 0), (799, border_width-1), 255, -1)  # 上
    cv2.rectangle(test_image, (0, 600-border_width), (799, 599), 255, -1)  # 下
    cv2.rectangle(test_image, (0, 0), (border_width-1, 599), 255, -1)  # 左
    cv2.rectangle(test_image, (800-border_width, 0), (799, 599), 255, -1)  # 右
    
    # 创建检测器并测试
    detector = BorderDetector()
    try:
        border_contour = detector.detect_border(test_image)
        
        if border_contour is not None:
            print("✓ 边框检测成功")
            
            # 获取轮廓信息
            info = detector.get_contour_info(border_contour)
            print(f"✓ 轮廓面积: {info['area']:.0f}")
            print(f"✓ 轮廓周长: {info['perimeter']:.0f}")
            print(f"✓ 顶点数量: {info['vertices_count']}")
            print(f"✓ 宽高比: {info['aspect_ratio']:.2f}")
            print(f"✓ 质量分数: {info['quality_score']:.1f}")
            print(f"✓ 是否凸形: {info['is_convex']}")
            
            return True
        else:
            print("❌ 未检测到边框")
            return False
            
    except Exception as e:
        print(f"❌ 边框检测失败: {e}")
        return False


if __name__ == "__main__":
    test_border_detector()
