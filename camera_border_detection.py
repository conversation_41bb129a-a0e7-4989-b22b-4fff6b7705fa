#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄像头实时边框检测
简化版本：只用线条框出内外边框
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import cv2
    import numpy as np
    from config import *
    from src.image_processor import ImageProcessor
    from src.border_detector import BorderDetector
    from src.coordinate_calculator import CoordinateCalculator
    print("✓ 所有模块导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保已安装OpenCV和所有模块文件存在")
    sys.exit(1)


class CameraBorderDetector:
    """摄像头边框检测器"""
    
    def __init__(self, camera_id=0, dpi=72):
        """
        初始化摄像头边框检测器
        Args:
            camera_id: 摄像头ID (默认0)
            dpi: 图像DPI (默认72)
        """
        # 初始化各模块
        self.processor = ImageProcessor()
        self.detector = BorderDetector()
        self.calculator = CoordinateCalculator(dpi=dpi)
        
        # 摄像头设置
        self.camera_id = camera_id
        self.cap = None
        
        # 显示设置
        self.window_name = "Border Detection - Press 'q' to quit, 's' to save"
        self.show_debug = False  # 是否显示调试信息
        
        # 性能统计
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
        print(f"✓ 摄像头边框检测器初始化完成 (DPI: {dpi})")
    
    def init_camera(self):
        """初始化摄像头"""
        try:
            self.cap = cv2.VideoCapture(self.camera_id)
            
            if not self.cap.isOpened():
                print(f"❌ 无法打开摄像头 {self.camera_id}")
                return False
            
            # 设置摄像头参数
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            # 获取实际参数
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = int(self.cap.get(cv2.CAP_PROP_FPS))
            
            print(f"✓ 摄像头初始化成功: {width}x{height} @ {fps}FPS")
            return True
            
        except Exception as e:
            print(f"❌ 摄像头初始化失败: {e}")
            return False
    
    def detect_frame(self, frame):
        """
        检测单帧图像中的边框
        Args:
            frame: 输入帧
        Returns:
            tuple: (outer_rect, inner_rect) 或 (None, None)
        """
        try:
            # 1. 图像预处理
            binary = self.processor.preprocess(frame)
            
            # 2. 边框检测
            contour = self.detector.detect_border(binary)
            if contour is None:
                return None, None
            
            # 3. 坐标计算
            outer_rect, inner_rect = self.calculator.calculate_boundaries(contour)
            return outer_rect, inner_rect
            
        except Exception as e:
            if self.show_debug:
                print(f"检测失败: {e}")
            return None, None
    
    def draw_borders(self, frame, outer_rect, inner_rect):
        """
        在帧上绘制边框线条
        Args:
            frame: 输入帧
            outer_rect: 外边界矩形 (x, y, w, h)
            inner_rect: 内边界矩形 (x, y, w, h)
        Returns:
            frame: 绘制了边框的帧
        """
        if outer_rect is None or inner_rect is None:
            return frame
        
        # 绘制外边框 (绿色)
        x1, y1, w1, h1 = outer_rect
        cv2.rectangle(frame, (x1, y1), (x1 + w1, y1 + h1), (0, 255, 0), 2)
        
        # 绘制内边框 (红色)
        x2, y2, w2, h2 = inner_rect
        cv2.rectangle(frame, (x2, y2), (x2 + w2, y2 + h2), (0, 0, 255), 2)
        
        return frame
    
    def add_info_text(self, frame, outer_rect, inner_rect):
        """
        添加信息文字
        Args:
            frame: 输入帧
            outer_rect: 外边界矩形
            inner_rect: 内边界矩形
        Returns:
            frame: 添加了信息的帧
        """
        # 添加FPS信息
        cv2.putText(frame, f"FPS: {self.current_fps:.1f}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 添加检测状态
        if outer_rect is not None and inner_rect is not None:
            cv2.putText(frame, "Border Detected", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 显示边框信息
            border_width = inner_rect[0] - outer_rect[0]
            border_width_cm = self.calculator.pixel_to_cm(border_width)
            cv2.putText(frame, f"Border: {border_width}px ({border_width_cm:.1f}cm)", 
                       (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        else:
            cv2.putText(frame, "No Border Found", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # 添加操作提示
        cv2.putText(frame, "Press 'q' to quit, 's' to save", (10, frame.shape[0] - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return frame
    
    def update_fps(self):
        """更新FPS计算"""
        self.fps_counter += 1
        current_time = time.time()
        
        if current_time - self.fps_start_time >= 1.0:  # 每秒更新一次
            self.current_fps = self.fps_counter / (current_time - self.fps_start_time)
            self.fps_counter = 0
            self.fps_start_time = current_time
    
    def save_frame(self, frame, outer_rect, inner_rect):
        """保存当前帧"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"border_detection_{timestamp}.jpg"
        
        try:
            cv2.imwrite(filename, frame)
            print(f"✓ 保存图像: {filename}")
            
            # 保存检测信息
            if outer_rect is not None and inner_rect is not None:
                info_filename = f"border_info_{timestamp}.txt"
                with open(info_filename, 'w', encoding='utf-8') as f:
                    f.write(f"边框检测结果 - {timestamp}\n")
                    f.write(f"外边界: {outer_rect}\n")
                    f.write(f"内边界: {inner_rect}\n")
                    border_width = inner_rect[0] - outer_rect[0]
                    border_width_cm = self.calculator.pixel_to_cm(border_width)
                    f.write(f"边框宽度: {border_width}像素 ({border_width_cm:.2f}厘米)\n")
                print(f"✓ 保存信息: {info_filename}")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def run(self):
        """运行实时检测"""
        if not self.init_camera():
            return False
        
        print("=== 开始实时边框检测 ===")
        print("操作说明:")
        print("  - 按 'q' 退出")
        print("  - 按 's' 保存当前帧")
        print("  - 按 'd' 切换调试模式")
        print("  - 将A4纸放在摄像头前进行检测")
        
        try:
            while True:
                # 读取帧
                ret, frame = self.cap.read()
                if not ret:
                    print("❌ 无法读取摄像头帧")
                    break
                
                # 检测边框
                outer_rect, inner_rect = self.detect_frame(frame)
                
                # 绘制边框
                frame = self.draw_borders(frame, outer_rect, inner_rect)
                
                # 添加信息文字
                frame = self.add_info_text(frame, outer_rect, inner_rect)
                
                # 更新FPS
                self.update_fps()
                
                # 显示结果
                cv2.imshow(self.window_name, frame)
                
                # 处理按键
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("退出检测")
                    break
                elif key == ord('s'):
                    self.save_frame(frame, outer_rect, inner_rect)
                elif key == ord('d'):
                    self.show_debug = not self.show_debug
                    print(f"调试模式: {'开启' if self.show_debug else '关闭'}")
            
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断检测")
            return True
        except Exception as e:
            print(f"❌ 运行时错误: {e}")
            return False
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.cap is not None:
            self.cap.release()
        cv2.destroyAllWindows()
        print("✓ 资源清理完成")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='摄像头实时边框检测')
    parser.add_argument('--camera', '-c', type=int, default=0, help='摄像头ID (默认: 0)')
    parser.add_argument('--dpi', type=int, default=72, help='图像DPI (默认: 72)')
    
    args = parser.parse_args()
    
    print("=== 摄像头实时边框检测系统 ===")
    
    # 创建检测器
    detector = CameraBorderDetector(camera_id=args.camera, dpi=args.dpi)
    
    # 运行检测
    success = detector.run()
    
    if success:
        print("✓ 检测完成")
        return 0
    else:
        print("❌ 检测失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
