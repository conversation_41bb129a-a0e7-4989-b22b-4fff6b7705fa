#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础摄像头测试
不依赖NumPy，只使用基本的OpenCV功能
"""

import sys
import time

def test_camera_only():
    """仅测试摄像头功能，不使用NumPy"""
    print("=== 基础摄像头测试 ===")
    
    try:
        import cv2
        print(f"✓ OpenCV版本: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV导入失败: {e}")
        return False
    
    print("尝试打开摄像头...")
    
    # 尝试打开摄像头
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("❌ 无法打开摄像头")
        print("可能的原因:")
        print("1. 摄像头未连接")
        print("2. 摄像头被其他程序占用")
        print("3. 权限不足")
        
        # 尝试其他摄像头ID
        for i in range(1, 4):
            print(f"尝试摄像头ID {i}...")
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                print(f"✓ 摄像头ID {i} 可用")
                break
            cap.release()
        else:
            return False
    
    print("✓ 摄像头打开成功")
    
    # 获取摄像头信息
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    
    print(f"摄像头参数: {width}x{height} @ {fps}FPS")
    
    # 设置窗口
    window_name = "Camera Test - Press 'q' to quit"
    
    print("\n开始摄像头预览...")
    print("操作说明:")
    print("- 按 'q' 退出")
    print("- 按 's' 保存当前帧")
    
    frame_count = 0
    start_time = time.time()
    
    try:
        while True:
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头帧")
                break
            
            frame_count += 1
            
            # 添加简单的文字信息（不使用NumPy）
            elapsed_time = time.time() - start_time
            if elapsed_time > 0:
                current_fps = frame_count / elapsed_time
                
                # 添加文字信息
                cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame, "Press 'q' to quit, 's' to save", (10, height - 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 显示帧
            cv2.imshow(window_name, frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("用户退出")
                break
            elif key == ord('s'):
                # 保存当前帧
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"camera_frame_{timestamp}.jpg"
                cv2.imwrite(filename, frame)
                print(f"✓ 保存图像: {filename}")
    
    except KeyboardInterrupt:
        print("\n用户中断 (Ctrl+C)")
    
    except Exception as e:
        print(f"❌ 运行时错误: {e}")
        return False
    
    finally:
        # 清理资源
        cap.release()
        cv2.destroyAllWindows()
        
        # 显示统计信息
        total_time = time.time() - start_time
        if total_time > 0:
            avg_fps = frame_count / total_time
            print(f"\n=== 统计信息 ===")
            print(f"总帧数: {frame_count}")
            print(f"运行时间: {total_time:.1f}秒")
            print(f"平均FPS: {avg_fps:.1f}")
    
    print("✓ 摄像头测试完成")
    return True

def check_system_info():
    """检查系统信息"""
    print("=== 系统信息检查 ===")
    
    # Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查OpenCV
    try:
        import cv2
        print(f"OpenCV版本: {cv2.__version__}")
        
        # 检查OpenCV构建信息
        build_info = cv2.getBuildInformation()
        if "FFMPEG" in build_info:
            print("✓ OpenCV支持视频编解码")
        else:
            print("⚠ OpenCV可能不支持某些视频格式")
            
    except ImportError:
        print("❌ OpenCV未安装")
        return False
    
    # 检查摄像头设备
    print("\n检查摄像头设备:")
    import os
    video_devices = []
    for i in range(10):
        device_path = f"/dev/video{i}"
        if os.path.exists(device_path):
            video_devices.append(device_path)
    
    if video_devices:
        print(f"找到视频设备: {video_devices}")
    else:
        print("❌ 未找到视频设备")
        print("请检查摄像头是否正确连接")
    
    return True

def install_suggestions():
    """提供安装建议"""
    print("\n=== 安装建议 ===")
    print("如果遇到依赖问题，请尝试以下命令:")
    print()
    print("1. 更新系统包:")
    print("   sudo apt update")
    print("   sudo apt upgrade")
    print()
    print("2. 安装系统依赖:")
    print("   sudo apt install libopenblas-dev liblapack-dev")
    print("   sudo apt install python3-opencv")
    print()
    print("3. 或者使用pip安装:")
    print("   pip3 install --user opencv-python-headless")
    print()
    print("4. 对于Jetson设备，可能需要:")
    print("   sudo apt install nvidia-opencv")

def main():
    """主函数"""
    print("=== 基础摄像头测试工具 ===")
    print("这个工具不依赖NumPy，只测试基本的摄像头功能")
    
    # 检查系统信息
    if not check_system_info():
        install_suggestions()
        return 1
    
    print("\n" + "="*50)
    
    # 运行摄像头测试
    success = test_camera_only()
    
    if success:
        print("\n🎉 摄像头测试成功！")
        print("您的摄像头工作正常，可以进行视频捕获")
        print("\n下一步:")
        print("1. 解决NumPy依赖问题以使用完整的边框检测功能")
        print("2. 或者使用这个基础版本进行简单的视频预览")
        return 0
    else:
        print("\n❌ 摄像头测试失败")
        install_suggestions()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
