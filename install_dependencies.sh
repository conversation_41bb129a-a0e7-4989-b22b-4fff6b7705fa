#!/bin/bash
# -*- coding: utf-8 -*-
"""
依赖安装脚本
自动安装边框检测系统所需的依赖
"""

echo "=== A4纸边框检测系统依赖安装 ==="
echo "这个脚本将帮助您安装必要的依赖包"
echo

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  请不要以root用户运行此脚本"
    echo "使用普通用户运行，需要时会提示输入密码"
    exit 1
fi

# 检查系统类型
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
    echo "检测到系统: $OS $VER"
else
    echo "❌ 无法检测系统类型"
    exit 1
fi

# 检查是否为Jetson设备
IS_JETSON=false
if [ -f /etc/nv_tegra_release ] || [ -d /usr/local/cuda ]; then
    IS_JETSON=true
    echo "✓ 检测到NVIDIA Jetson设备"
fi

echo

# 更新包列表
echo "📦 更新包列表..."
sudo apt update

# 安装系统依赖
echo "📦 安装系统依赖..."
sudo apt install -y \
    python3-pip \
    python3-dev \
    build-essential \
    cmake \
    pkg-config \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    libgtk-3-dev \
    libatlas-base-dev \
    gfortran

# 安装数学库
echo "📦 安装数学库..."
sudo apt install -y \
    libopenblas-dev \
    liblapack-dev \
    libblas-dev

# 根据设备类型安装OpenCV
if [ "$IS_JETSON" = true ]; then
    echo "🎯 为Jetson设备安装OpenCV..."
    
    # 尝试安装预编译的OpenCV
    sudo apt install -y python3-opencv
    
    # 如果失败，尝试其他方法
    if [ $? -ne 0 ]; then
        echo "⚠️  系统OpenCV安装失败，尝试pip安装..."
        pip3 install --user opencv-python-headless
    fi
    
    # 安装NumPy
    sudo apt install -y python3-numpy
    if [ $? -ne 0 ]; then
        pip3 install --user numpy
    fi
    
else
    echo "🖥️  为普通设备安装OpenCV..."
    
    # 先尝试系统包
    sudo apt install -y python3-opencv python3-numpy
    
    # 如果失败，使用pip
    if [ $? -ne 0 ]; then
        echo "⚠️  系统包安装失败，使用pip安装..."
        pip3 install --user opencv-python numpy matplotlib
    fi
fi

echo

# 验证安装
echo "🔍 验证安装..."

# 检查Python版本
python3 --version

# 检查OpenCV
echo "检查OpenCV..."
python3 -c "import cv2; print('OpenCV版本:', cv2.__version__)" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ OpenCV安装成功"
else
    echo "❌ OpenCV安装失败"
    INSTALL_FAILED=true
fi

# 检查NumPy
echo "检查NumPy..."
python3 -c "import numpy; print('NumPy版本:', numpy.__version__)" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ NumPy安装成功"
else
    echo "❌ NumPy安装失败"
    INSTALL_FAILED=true
fi

# 检查摄像头设备
echo "检查摄像头设备..."
if ls /dev/video* 1> /dev/null 2>&1; then
    echo "✅ 找到摄像头设备:"
    ls -la /dev/video*
else
    echo "⚠️  未找到摄像头设备"
    echo "请检查摄像头是否正确连接"
fi

echo

# 测试基本功能
if [ -z "$INSTALL_FAILED" ]; then
    echo "🧪 测试基本功能..."
    
    # 创建简单测试脚本
    cat > test_install.py << 'EOF'
#!/usr/bin/env python3
import sys
try:
    import cv2
    import numpy as np
    print("✅ 所有依赖导入成功")
    print(f"OpenCV: {cv2.__version__}")
    print(f"NumPy: {np.__version__}")
    
    # 测试基本功能
    img = np.ones((100, 100, 3), dtype=np.uint8) * 255
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    print("✅ 基本图像处理功能正常")
    
    # 测试摄像头
    cap = cv2.VideoCapture(0)
    if cap.isOpened():
        print("✅ 摄像头可以打开")
        cap.release()
    else:
        print("⚠️  摄像头无法打开")
    
    print("🎉 安装验证成功！")
    sys.exit(0)
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 测试失败: {e}")
    sys.exit(1)
EOF
    
    python3 test_install.py
    TEST_RESULT=$?
    rm -f test_install.py
    
    if [ $TEST_RESULT -eq 0 ]; then
        echo
        echo "🎉 依赖安装完成！"
        echo "现在可以运行边框检测系统:"
        echo "  python3 camera_border_detection.py"
        echo "  python3 demo_border_detection.py"
    else
        echo
        echo "❌ 安装验证失败"
        INSTALL_FAILED=true
    fi
fi

# 如果安装失败，提供故障排除建议
if [ ! -z "$INSTALL_FAILED" ]; then
    echo
    echo "🔧 故障排除建议:"
    echo
    echo "1. 手动安装NumPy:"
    echo "   pip3 install --user --upgrade numpy"
    echo
    echo "2. 手动安装OpenCV:"
    echo "   pip3 install --user opencv-python-headless"
    echo
    echo "3. 如果是Jetson设备，尝试:"
    echo "   sudo apt install nvidia-opencv"
    echo
    echo "4. 重启系统后再次尝试"
    echo
    echo "5. 检查Python路径:"
    echo "   python3 -c \"import sys; print(sys.path)\""
    echo
    echo "6. 如果仍有问题，可以运行演示版本:"
    echo "   python3 demo_border_detection.py"
fi

echo
echo "=== 安装脚本完成 ==="
