#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试模块
测试系统在不同条件下的性能表现
"""

import sys
import os
import time
import statistics
import tempfile
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.border_detection_system import BorderDetectionSystem
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"导入失败: {e}")
    OPENCV_AVAILABLE = False


class PerformanceTester:
    """性能测试器"""
    
    def __init__(self):
        """初始化性能测试器"""
        self.system = None
        if OPENCV_AVAILABLE:
            self.system = BorderDetectionSystem(dpi=72)
        
        self.test_results = {
            'processing_times': [],
            'memory_usage': [],
            'success_rates': [],
            'image_sizes': []
        }
    
    def create_test_images(self, sizes):
        """创建不同尺寸的测试图像"""
        if not OPENCV_AVAILABLE:
            return []
        
        images = []
        for width, height in sizes:
            # 创建A4纸图像
            image = np.ones((height, width, 3), dtype=np.uint8) * 255
            
            # 计算边框宽度（按比例）
            border_width = max(int(min(width, height) * 0.07), 20)
            
            # 绘制边框
            cv2.rectangle(image, (0, 0), (width-1, border_width-1), (0, 0, 0), -1)
            cv2.rectangle(image, (0, height-border_width), (width-1, height-1), (0, 0, 0), -1)
            cv2.rectangle(image, (0, 0), (border_width-1, height-1), (0, 0, 0), -1)
            cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), (0, 0, 0), -1)
            
            images.append(((width, height), image))
        
        return images
    
    def test_processing_speed(self):
        """测试处理速度"""
        print("=== 处理速度测试 ===")
        
        if not OPENCV_AVAILABLE:
            print("⚠ OpenCV不可用，跳过性能测试")
            return False
        
        # 测试不同尺寸的图像
        test_sizes = [
            (400, 300),   # 小尺寸
            (800, 600),   # 中等尺寸
            (1280, 960),  # 大尺寸
            (1920, 1440)  # 超大尺寸
        ]
        
        test_images = self.create_test_images(test_sizes)
        
        for (width, height), image in test_images:
            print(f"\n测试尺寸: {width}x{height}")
            
            # 保存临时图像
            temp_path = f"temp_test_{width}x{height}.jpg"
            cv2.imwrite(temp_path, image)
            
            # 多次测试取平均值
            times = []
            successes = 0
            
            for i in range(5):
                start_time = time.time()
                outer, inner, result = self.system.detect_boundaries(temp_path)
                end_time = time.time()
                
                processing_time = end_time - start_time
                times.append(processing_time)
                
                if outer is not None and inner is not None:
                    successes += 1
            
            # 统计结果
            avg_time = statistics.mean(times)
            success_rate = successes / len(times)
            
            print(f"  平均处理时间: {avg_time:.3f}秒")
            print(f"  成功率: {success_rate:.1%}")
            print(f"  处理速度: {1/avg_time:.1f} FPS")
            
            # 记录结果
            self.test_results['processing_times'].append(avg_time)
            self.test_results['success_rates'].append(success_rate)
            self.test_results['image_sizes'].append((width, height))
            
            # 清理临时文件
            try:
                os.remove(temp_path)
            except:
                pass
        
        return True
    
    def test_memory_usage(self):
        """测试内存使用"""
        print("\n=== 内存使用测试 ===")
        
        if not OPENCV_AVAILABLE:
            print("⚠ OpenCV不可用，跳过内存测试")
            return False
        
        try:
            import psutil
            process = psutil.Process()
        except ImportError:
            print("⚠ psutil不可用，跳过内存测试")
            return False
        
        # 创建大尺寸测试图像
        large_image = self.create_test_images([(1920, 1440)])[0][1]
        temp_path = "temp_memory_test.jpg"
        cv2.imwrite(temp_path, large_image)
        
        # 记录初始内存
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行多次检测
        for i in range(10):
            self.system.detect_boundaries(temp_path)
            
            current_memory = process.memory_info().rss / 1024 / 1024
            memory_usage = current_memory - initial_memory
            self.test_results['memory_usage'].append(memory_usage)
        
        avg_memory = statistics.mean(self.test_results['memory_usage'])
        max_memory = max(self.test_results['memory_usage'])
        
        print(f"初始内存: {initial_memory:.1f} MB")
        print(f"平均额外内存: {avg_memory:.1f} MB")
        print(f"峰值额外内存: {max_memory:.1f} MB")
        
        # 清理
        try:
            os.remove(temp_path)
        except:
            pass
        
        return True
    
    def test_batch_performance(self):
        """测试批量处理性能"""
        print("\n=== 批量处理性能测试 ===")
        
        if not OPENCV_AVAILABLE:
            print("⚠ OpenCV不可用，跳过批量测试")
            return False
        
        # 创建多个测试图像
        batch_sizes = [5, 10, 20]
        
        for batch_size in batch_sizes:
            print(f"\n批量大小: {batch_size}")
            
            # 创建临时目录和图像
            temp_dir = tempfile.mkdtemp()
            image_paths = []
            
            base_image = self.create_test_images([(800, 600)])[0][1]
            
            for i in range(batch_size):
                path = os.path.join(temp_dir, f"test_{i}.jpg")
                cv2.imwrite(path, base_image)
                image_paths.append(path)
            
            # 测试批量处理
            start_time = time.time()
            results = self.system.batch_process(image_paths, save_results=False)
            end_time = time.time()
            
            total_time = end_time - start_time
            avg_time_per_image = total_time / batch_size
            successful = sum(1 for r in results if r['success'])
            
            print(f"  总处理时间: {total_time:.3f}秒")
            print(f"  平均每张: {avg_time_per_image:.3f}秒")
            print(f"  成功处理: {successful}/{batch_size}")
            print(f"  处理速度: {batch_size/total_time:.1f} 张/秒")
            
            # 清理临时文件
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        return True
    
    def test_stress_conditions(self):
        """测试压力条件"""
        print("\n=== 压力条件测试 ===")
        
        if not OPENCV_AVAILABLE:
            print("⚠ OpenCV不可用，跳过压力测试")
            return False
        
        # 创建各种困难条件的图像
        base_image = self.create_test_images([(800, 600)])[0][1]
        
        stress_tests = [
            ("高噪声", lambda img: self._add_noise(img, 50)),
            ("低对比度", lambda img: self._reduce_contrast(img)),
            ("模糊", lambda img: self._add_blur(img)),
            ("旋转", lambda img: self._rotate_image(img, 10))
        ]
        
        for test_name, transform_func in stress_tests:
            print(f"\n{test_name}测试:")
            
            # 应用变换
            transformed_image = transform_func(base_image)
            temp_path = f"temp_stress_{test_name}.jpg"
            cv2.imwrite(temp_path, transformed_image)
            
            # 测试检测
            successes = 0
            times = []
            
            for i in range(5):
                start_time = time.time()
                outer, inner, result = self.system.detect_boundaries(temp_path)
                end_time = time.time()
                
                times.append(end_time - start_time)
                if outer is not None and inner is not None:
                    successes += 1
            
            success_rate = successes / len(times)
            avg_time = statistics.mean(times)
            
            print(f"  成功率: {success_rate:.1%}")
            print(f"  平均时间: {avg_time:.3f}秒")
            
            # 清理
            try:
                os.remove(temp_path)
            except:
                pass
        
        return True
    
    def _add_noise(self, image, noise_level):
        """添加噪声"""
        noise = np.random.normal(0, noise_level, image.shape).astype(np.int16)
        return np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    def _reduce_contrast(self, image):
        """降低对比度"""
        low_contrast = image.copy()
        mask = np.all(low_contrast == [0, 0, 0], axis=2)
        low_contrast[mask] = [80, 80, 80]  # 黑色变为深灰色
        return low_contrast
    
    def _add_blur(self, image):
        """添加模糊"""
        return cv2.GaussianBlur(image, (15, 15), 0)
    
    def _rotate_image(self, image, angle):
        """旋转图像"""
        h, w = image.shape[:2]
        center = (w // 2, h // 2)
        matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        return cv2.warpAffine(image, matrix, (w, h), borderValue=(255, 255, 255))
    
    def generate_report(self):
        """生成性能报告"""
        print("\n=== 性能测试报告 ===")
        
        if not self.test_results['processing_times']:
            print("无性能数据可用")
            return
        
        # 处理时间统计
        times = self.test_results['processing_times']
        print(f"处理时间统计:")
        print(f"  平均: {statistics.mean(times):.3f}秒")
        print(f"  最快: {min(times):.3f}秒")
        print(f"  最慢: {max(times):.3f}秒")
        
        # 成功率统计
        if self.test_results['success_rates']:
            rates = self.test_results['success_rates']
            print(f"成功率统计:")
            print(f"  平均: {statistics.mean(rates):.1%}")
            print(f"  最高: {max(rates):.1%}")
            print(f"  最低: {min(rates):.1%}")
        
        # 内存使用统计
        if self.test_results['memory_usage']:
            memory = self.test_results['memory_usage']
            print(f"内存使用统计:")
            print(f"  平均: {statistics.mean(memory):.1f} MB")
            print(f"  峰值: {max(memory):.1f} MB")


def main():
    """主测试函数"""
    print("=== 性能测试套件 ===")
    
    tester = PerformanceTester()
    
    # 执行各项性能测试
    tests = [
        ("处理速度", tester.test_processing_speed),
        ("内存使用", tester.test_memory_usage),
        ("批量处理", tester.test_batch_performance),
        ("压力条件", tester.test_stress_conditions)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name}测试 ---")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name}测试失败: {e}")
            results.append(False)
    
    # 生成报告
    tester.generate_report()
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 性能测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("✅ 所有性能测试通过！")
        return 0
    else:
        print("❌ 部分性能测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
