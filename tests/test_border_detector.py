#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
边框检测模块测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.border_detector import BorderDetector
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"导入失败: {e}")
    print("跳过OpenCV相关测试")
    OPENCV_AVAILABLE = False


def create_test_binary_image():
    """创建测试用的二值化图像（模拟预处理后的结果）"""
    if not OPENCV_AVAILABLE:
        return None
    
    # 创建黑色背景
    height, width = 600, 800
    image = np.zeros((height, width), dtype=np.uint8)
    
    # 添加白色边框（模拟预处理后的边框，黑色边框在二值化后变为白色）
    border_width = int(2 * 28.35)  # 2cm约57像素
    
    # 绘制边框
    cv2.rectangle(image, (0, 0), (width-1, border_width-1), 255, -1)  # 上
    cv2.rectangle(image, (0, height-border_width), (width-1, height-1), 255, -1)  # 下
    cv2.rectangle(image, (0, 0), (border_width-1, height-1), 255, -1)  # 左
    cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), 255, -1)  # 右
    
    return image


def create_noisy_test_image():
    """创建带噪声的测试图像"""
    if not OPENCV_AVAILABLE:
        return None
    
    base_image = create_test_binary_image()
    
    # 添加一些噪声点
    noise_points = np.random.randint(0, 2, (50, 2)) * [800, 600]
    for point in noise_points:
        cv2.circle(base_image, tuple(point), 3, 255, -1)
    
    # 添加一些干扰矩形
    cv2.rectangle(base_image, (200, 200), (250, 250), 255, -1)
    cv2.rectangle(base_image, (500, 300), (580, 380), 255, -1)
    
    return base_image


def test_basic_detection():
    """测试基本边框检测功能"""
    print("1. 测试基本边框检测功能...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        # 创建测试图像
        test_image = create_test_binary_image()
        
        # 创建检测器
        detector = BorderDetector()
        
        # 执行检测
        border_contour = detector.detect_border(test_image)
        
        # 验证结果
        assert border_contour is not None, "应该检测到边框轮廓"
        
        # 获取轮廓信息
        info = detector.get_contour_info(border_contour)
        assert info is not None, "轮廓信息不应为空"
        assert info['vertices_count'] == 4, f"矩形应该有4个顶点，实际有{info['vertices_count']}个"
        assert info['area'] > 1000, f"轮廓面积应该大于1000，实际为{info['area']}"
        assert info['is_convex'], "边框轮廓应该是凸形的"
        
        print("✓ 基本边框检测功能测试通过")
        print(f"  - 轮廓面积: {info['area']:.0f}")
        print(f"  - 顶点数量: {info['vertices_count']}")
        print(f"  - 质量分数: {info['quality_score']:.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本边框检测功能测试失败: {e}")
        return False


def test_noise_resistance():
    """测试抗噪声能力"""
    print("2. 测试抗噪声能力...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        # 创建带噪声的测试图像
        noisy_image = create_noisy_test_image()
        
        detector = BorderDetector()
        border_contour = detector.detect_border(noisy_image)
        
        # 验证结果
        assert border_contour is not None, "即使有噪声也应该检测到边框"
        
        info = detector.get_contour_info(border_contour)
        assert info['vertices_count'] == 4, "应该检测到矩形边框"
        
        # 检查检测到的边框是否是主要边框（面积应该足够大）
        assert info['area'] > 10000, f"主边框面积应该足够大，实际为{info['area']}"
        
        print("✓ 抗噪声能力测试通过")
        print(f"  - 在噪声环境下仍能检测到面积为{info['area']:.0f}的边框")
        
        return True
        
    except Exception as e:
        print(f"❌ 抗噪声能力测试失败: {e}")
        return False


def test_edge_cases():
    """测试边界情况"""
    print("3. 测试边界情况...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    detector = BorderDetector()
    
    try:
        # 测试空图像
        try:
            detector.detect_border(None)
            assert False, "应该抛出异常"
        except ValueError:
            print("✓ 空图像异常处理正确")
        
        # 测试非灰度图像
        try:
            color_image = np.ones((100, 100, 3), dtype=np.uint8)
            detector.detect_border(color_image)
            assert False, "应该抛出异常"
        except ValueError:
            print("✓ 非灰度图像异常处理正确")
        
        # 测试无边框图像
        empty_image = np.zeros((100, 100), dtype=np.uint8)
        result = detector.detect_border(empty_image)
        assert result is None, "无边框图像应该返回None"
        print("✓ 无边框图像处理正确")
        
        # 测试极小图像
        tiny_image = np.ones((10, 10), dtype=np.uint8) * 255
        result = detector.detect_border(tiny_image)
        # 极小图像可能检测不到边框，这是正常的
        print("✓ 极小图像处理正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        return False


def test_parameter_sensitivity():
    """测试参数敏感性"""
    print("4. 测试参数敏感性...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        test_image = create_test_binary_image()
        
        # 测试不同的Canny阈值
        canny_params = [(30, 100), (50, 150), (70, 200)]
        
        for low, high in canny_params:
            detector = BorderDetector()
            detector.canny_low = low
            detector.canny_high = high
            
            border_contour = detector.detect_border(test_image)
            
            if border_contour is not None:
                info = detector.get_contour_info(border_contour)
                print(f"  - Canny({low},{high}): 检测成功，面积={info['area']:.0f}")
            else:
                print(f"  - Canny({low},{high}): 检测失败")
        
        print("✓ 参数敏感性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 参数敏感性测试失败: {e}")
        return False


def test_debug_functionality():
    """测试调试功能"""
    print("5. 测试调试功能...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        test_image = create_test_binary_image()
        detector = BorderDetector()
        
        # 测试调试显示功能
        debug_results = detector.debug_show_detection_steps(test_image)
        
        # 验证调试结果
        expected_keys = ['original', 'edges', 'all_contours', 'border_contour', 
                        'vis_edges', 'vis_contours', 'vis_result', 'contour_count', 'border_found']
        
        for key in expected_keys:
            assert key in debug_results, f"调试结果缺少 {key}"
        
        assert debug_results['contour_count'] > 0, "应该检测到轮廓"
        assert debug_results['border_found'], "应该找到边框"
        
        print("✓ 调试功能测试通过")
        print(f"  - 检测到 {debug_results['contour_count']} 个轮廓")
        print(f"  - 边框检测: {'成功' if debug_results['border_found'] else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=== 边框检测模块测试 ===")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV环境不可用，将跳过大部分测试")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_basic_detection())
    test_results.append(test_noise_resistance())
    test_results.append(test_edge_cases())
    test_results.append(test_parameter_sensitivity())
    test_results.append(test_debug_functionality())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("✅ 所有测试通过！")
        if not OPENCV_AVAILABLE:
            print("📝 注意: 由于OpenCV不可用，部分测试被跳过")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
