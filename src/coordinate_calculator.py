#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标计算与边界定位模块
实现像素与物理尺寸的转换，计算边框的内外边界坐标
"""

import sys
import os
import math
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from config import *
except ImportError as e:
    print(f"导入依赖失败: {e}")
    print("请确保已安装OpenCV和NumPy")
    sys.exit(1)


class CoordinateCalculator:
    """坐标计算类，处理像素与物理尺寸转换，计算内外边界坐标"""
    
    def __init__(self, pixels_per_cm=None, dpi=None):
        """
        初始化坐标计算器
        Args:
            pixels_per_cm: 像素密度(像素/厘米)，优先级高于dpi
            dpi: 图像DPI，如果未提供pixels_per_cm则根据dpi计算
        """
        if pixels_per_cm is not None:
            self.pixels_per_cm = pixels_per_cm
        elif dpi is not None:
            # 1英寸 = 2.54厘米，所以 pixels_per_cm = dpi / 2.54
            self.pixels_per_cm = dpi / 2.54
        else:
            # 使用默认值（72DPI）
            self.pixels_per_cm = PIXELS_PER_CM
        
        self.border_width_cm = BORDER_WIDTH_CM  # 边框宽度(厘米)
        self.border_width_pixels = int(self.border_width_cm * self.pixels_per_cm)  # 边框宽度(像素)
        
        # 预定义常用DPI对应的像素密度
        self.dpi_presets = {
            72: DPI_72,
            96: DPI_96,
            150: DPI_150,
            300: DPI_300
        }
    
    def calculate_boundaries(self, border_contour):
        """
        计算边框的内外边界坐标
        Args:
            border_contour: 边框轮廓
        Returns:
            tuple: (outer_rect, inner_rect) 外边界和内边界矩形
        """
        if border_contour is None:
            raise ValueError("边框轮廓不能为空")
        
        # 计算外边界（轮廓的边界矩形）
        outer_rect = cv2.boundingRect(border_contour)
        x, y, w, h = outer_rect
        
        # 计算内边界（向内收缩边框宽度）
        inner_x = x + self.border_width_pixels
        inner_y = y + self.border_width_pixels
        inner_w = w - 2 * self.border_width_pixels
        inner_h = h - 2 * self.border_width_pixels
        
        # 验证内边界的有效性
        if inner_w <= 0 or inner_h <= 0:
            raise ValueError(f"内边界尺寸无效: 宽度={inner_w}, 高度={inner_h}")
        
        inner_rect = (inner_x, inner_y, inner_w, inner_h)
        
        return outer_rect, inner_rect
    
    def calculate_precise_boundaries(self, border_contour):
        """
        计算精确的边界坐标（基于轮廓的实际形状）
        Args:
            border_contour: 边框轮廓
        Returns:
            dict: 包含详细边界信息的字典
        """
        if border_contour is None:
            raise ValueError("边框轮廓不能为空")
        
        # 获取轮廓的凸包
        hull = cv2.convexHull(border_contour)
        
        # 近似为矩形
        epsilon = 0.02 * cv2.arcLength(hull, True)
        approx = cv2.approxPolyDP(hull, epsilon, True)
        
        # 如果近似结果不是4个顶点，使用边界矩形
        if len(approx) != 4:
            return self._calculate_rect_boundaries(border_contour)
        
        # 对4个顶点进行排序：左上、右上、右下、左下
        vertices = self._sort_vertices(approx.reshape(-1, 2))
        
        # 计算内边界顶点
        inner_vertices = self._calculate_inner_vertices(vertices)
        
        result = {
            'outer_vertices': vertices.tolist(),
            'inner_vertices': inner_vertices.tolist(),
            'outer_rect': cv2.boundingRect(border_contour),
            'inner_rect': self._vertices_to_rect(inner_vertices),
            'border_width_pixels': self.border_width_pixels,
            'border_width_cm': self.border_width_cm,
            'pixels_per_cm': self.pixels_per_cm
        }
        
        return result
    
    def _calculate_rect_boundaries(self, border_contour):
        """计算矩形边界（fallback方法）"""
        outer_rect, inner_rect = self.calculate_boundaries(border_contour)
        x, y, w, h = outer_rect
        
        # 构造矩形顶点
        outer_vertices = np.array([
            [x, y],           # 左上
            [x + w, y],       # 右上
            [x + w, y + h],   # 右下
            [x, y + h]        # 左下
        ])
        
        ix, iy, iw, ih = inner_rect
        inner_vertices = np.array([
            [ix, iy],           # 左上
            [ix + iw, iy],      # 右上
            [ix + iw, iy + ih], # 右下
            [ix, iy + ih]       # 左下
        ])
        
        return {
            'outer_vertices': outer_vertices.tolist(),
            'inner_vertices': inner_vertices.tolist(),
            'outer_rect': outer_rect,
            'inner_rect': inner_rect,
            'border_width_pixels': self.border_width_pixels,
            'border_width_cm': self.border_width_cm,
            'pixels_per_cm': self.pixels_per_cm
        }
    
    def _sort_vertices(self, vertices):
        """
        对4个顶点进行排序：左上、右上、右下、左下
        Args:
            vertices: 4个顶点的坐标数组
        Returns:
            sorted_vertices: 排序后的顶点数组
        """
        # 计算质心
        center = np.mean(vertices, axis=0)
        
        # 计算每个点相对于质心的角度
        angles = []
        for vertex in vertices:
            angle = math.atan2(vertex[1] - center[1], vertex[0] - center[0])
            angles.append(angle)
        
        # 按角度排序，从左上开始（角度约为-3π/4）
        sorted_indices = sorted(range(4), key=lambda i: angles[i])
        
        # 重新排列，确保顺序为：左上、右上、右下、左下
        sorted_vertices = vertices[sorted_indices]
        
        # 进一步确保正确的顺序
        # 找到最左上的点作为起始点
        top_left_idx = np.argmin(sorted_vertices[:, 0] + sorted_vertices[:, 1])
        
        # 重新排列数组，使左上角点在第一位
        sorted_vertices = np.roll(sorted_vertices, -top_left_idx, axis=0)
        
        return sorted_vertices
    
    def _calculate_inner_vertices(self, outer_vertices):
        """
        根据外边界顶点计算内边界顶点
        Args:
            outer_vertices: 外边界顶点（左上、右上、右下、左下）
        Returns:
            inner_vertices: 内边界顶点
        """
        # 计算每条边的内缩向量
        border_offset = self.border_width_pixels
        
        # 简化处理：假设是矩形，直接向内收缩
        inner_vertices = np.array([
            [outer_vertices[0][0] + border_offset, outer_vertices[0][1] + border_offset],  # 左上
            [outer_vertices[1][0] - border_offset, outer_vertices[1][1] + border_offset],  # 右上
            [outer_vertices[2][0] - border_offset, outer_vertices[2][1] - border_offset],  # 右下
            [outer_vertices[3][0] + border_offset, outer_vertices[3][1] - border_offset]   # 左下
        ])
        
        return inner_vertices
    
    def _vertices_to_rect(self, vertices):
        """
        将顶点坐标转换为矩形格式(x, y, w, h)
        Args:
            vertices: 顶点坐标数组
        Returns:
            rect: (x, y, w, h)格式的矩形
        """
        x_coords = vertices[:, 0]
        y_coords = vertices[:, 1]
        
        x = int(np.min(x_coords))
        y = int(np.min(y_coords))
        w = int(np.max(x_coords) - x)
        h = int(np.max(y_coords) - y)
        
        return (x, y, w, h)
    
    def pixel_to_cm(self, pixel_value):
        """
        像素转厘米
        Args:
            pixel_value: 像素值
        Returns:
            cm_value: 厘米值
        """
        return pixel_value / self.pixels_per_cm
    
    def cm_to_pixel(self, cm_value):
        """
        厘米转像素
        Args:
            cm_value: 厘米值
        Returns:
            pixel_value: 像素值
        """
        return int(cm_value * self.pixels_per_cm)
    
    def set_dpi(self, dpi):
        """
        设置DPI并更新像素密度
        Args:
            dpi: 新的DPI值
        """
        if dpi in self.dpi_presets:
            self.pixels_per_cm = self.dpi_presets[dpi]
        else:
            self.pixels_per_cm = dpi / 2.54
        
        # 更新边框宽度像素值
        self.border_width_pixels = int(self.border_width_cm * self.pixels_per_cm)
    
    def get_boundary_info(self, border_contour):
        """
        获取边界的详细信息
        Args:
            border_contour: 边框轮廓
        Returns:
            info: 边界信息字典
        """
        try:
            # 计算精确边界
            precise_boundaries = self.calculate_precise_boundaries(border_contour)
            
            # 计算面积
            outer_area_pixels = cv2.contourArea(border_contour)
            outer_area_cm2 = outer_area_pixels / (self.pixels_per_cm ** 2)
            
            # 计算内边界面积
            inner_vertices = np.array(precise_boundaries['inner_vertices'], dtype=np.int32)
            inner_area_pixels = cv2.contourArea(inner_vertices)
            inner_area_cm2 = inner_area_pixels / (self.pixels_per_cm ** 2)
            
            # 计算边框面积
            border_area_pixels = outer_area_pixels - inner_area_pixels
            border_area_cm2 = border_area_pixels / (self.pixels_per_cm ** 2)
            
            info = {
                'boundaries': precise_boundaries,
                'areas': {
                    'outer_pixels': outer_area_pixels,
                    'outer_cm2': outer_area_cm2,
                    'inner_pixels': inner_area_pixels,
                    'inner_cm2': inner_area_cm2,
                    'border_pixels': border_area_pixels,
                    'border_cm2': border_area_cm2
                },
                'dimensions': {
                    'outer_width_pixels': precise_boundaries['outer_rect'][2],
                    'outer_height_pixels': precise_boundaries['outer_rect'][3],
                    'outer_width_cm': self.pixel_to_cm(precise_boundaries['outer_rect'][2]),
                    'outer_height_cm': self.pixel_to_cm(precise_boundaries['outer_rect'][3]),
                    'inner_width_pixels': precise_boundaries['inner_rect'][2],
                    'inner_height_pixels': precise_boundaries['inner_rect'][3],
                    'inner_width_cm': self.pixel_to_cm(precise_boundaries['inner_rect'][2]),
                    'inner_height_cm': self.pixel_to_cm(precise_boundaries['inner_rect'][3])
                },
                'settings': {
                    'pixels_per_cm': self.pixels_per_cm,
                    'border_width_cm': self.border_width_cm,
                    'border_width_pixels': self.border_width_pixels
                }
            }
            
            return info
            
        except Exception as e:
            raise ValueError(f"获取边界信息失败: {e}")


def test_coordinate_calculator():
    """测试坐标计算器"""
    print("=== 坐标计算模块测试 ===")
    
    # 创建测试轮廓（模拟边框检测结果）
    test_contour = np.array([
        [[50, 50]],
        [[750, 50]],
        [[750, 550]],
        [[50, 550]]
    ], dtype=np.int32)
    
    # 创建计算器并测试
    calculator = CoordinateCalculator()
    
    try:
        # 测试基本边界计算
        outer_rect, inner_rect = calculator.calculate_boundaries(test_contour)
        
        print("✓ 基本边界计算成功")
        print(f"  外边界: {outer_rect}")
        print(f"  内边界: {inner_rect}")
        
        # 测试精确边界计算
        precise_info = calculator.calculate_precise_boundaries(test_contour)
        print("✓ 精确边界计算成功")
        print(f"  外边界顶点: {len(precise_info['outer_vertices'])}个")
        print(f"  内边界顶点: {len(precise_info['inner_vertices'])}个")
        
        # 测试单位转换
        test_pixels = 100
        cm_value = calculator.pixel_to_cm(test_pixels)
        pixels_back = calculator.cm_to_pixel(cm_value)
        print(f"✓ 单位转换: {test_pixels}像素 = {cm_value:.2f}厘米 = {pixels_back}像素")
        
        return True
        
    except Exception as e:
        print(f"❌ 坐标计算测试失败: {e}")
        return False


if __name__ == "__main__":
    test_coordinate_calculator()
