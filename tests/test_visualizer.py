#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.visualizer import Visualizer
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"导入失败: {e}")
    print("跳过OpenCV相关测试")
    OPENCV_AVAILABLE = False


def create_test_image():
    """创建测试图像"""
    if not OPENCV_AVAILABLE:
        return None
    
    # 创建白色背景图像
    image = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # 添加一些内容模拟A4纸
    cv2.rectangle(image, (50, 50), (550, 350), (240, 240, 240), -1)  # 浅灰色背景
    cv2.putText(image, "Sample A4 Document", (150, 150), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    cv2.putText(image, "Border Detection Test", (130, 200), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    return image


def test_basic_visualization():
    """测试基本可视化功能"""
    print("1. 测试基本可视化功能...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        # 创建测试数据
        test_image = create_test_image()
        outer_rect = (50, 50, 500, 300)
        inner_rect = (100, 100, 400, 200)
        
        # 创建可视化器
        visualizer = Visualizer()
        
        # 测试边界绘制
        result = visualizer.draw_boundaries(test_image, outer_rect, inner_rect)
        
        # 验证结果
        assert result is not None, "绘制结果不应为空"
        assert result.shape == test_image.shape, "结果图像尺寸应与原图相同"
        assert not np.array_equal(result, test_image), "结果图像应与原图不同"
        
        print("✓ 基本边界绘制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本可视化功能测试失败: {e}")
        return False


def test_precise_visualization():
    """测试精确可视化功能"""
    print("2. 测试精确可视化功能...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        test_image = create_test_image()
        
        # 定义精确顶点
        outer_vertices = [[50, 50], [550, 50], [550, 350], [50, 350]]
        inner_vertices = [[100, 100], [500, 100], [500, 300], [100, 300]]
        
        visualizer = Visualizer()
        
        # 测试精确边界绘制
        result = visualizer.draw_precise_boundaries(test_image, outer_vertices, inner_vertices)
        
        # 验证结果
        assert result is not None, "精确绘制结果不应为空"
        assert result.shape == test_image.shape, "结果图像尺寸应与原图相同"
        
        print("✓ 精确边界绘制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 精确可视化功能测试失败: {e}")
        return False


def test_info_overlay():
    """测试信息覆盖层功能"""
    print("3. 测试信息覆盖层功能...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        test_image = create_test_image()
        
        # 准备信息字典
        info_dict = {
            "外边界": "(50, 50, 500, 300)",
            "内边界": "(100, 100, 400, 200)",
            "边框宽度": "50像素",
            "检测状态": "成功"
        }
        
        visualizer = Visualizer()
        
        # 测试信息覆盖层
        result = visualizer.add_info_overlay(test_image, info_dict)
        
        # 验证结果
        assert result is not None, "信息覆盖层结果不应为空"
        assert result.shape == test_image.shape, "结果图像尺寸应与原图相同"
        
        print("✓ 信息覆盖层测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 信息覆盖层功能测试失败: {e}")
        return False


def test_image_saving():
    """测试图像保存功能"""
    print("4. 测试图像保存功能...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        test_image = create_test_image()
        visualizer = Visualizer()
        
        # 测试保存功能
        test_filename = "examples/test_save.jpg"
        success = visualizer.save_result(test_image, test_filename)
        
        if success:
            # 验证文件是否存在
            if os.path.exists(test_filename):
                print("✓ 图像保存测试通过")
                # 清理测试文件
                try:
                    os.remove(test_filename)
                except:
                    pass
                return True
            else:
                print("❌ 保存的文件不存在")
                return False
        else:
            print("⚠ 图像保存失败（可能是权限问题）")
            return True  # 不算作失败，可能是环境问题
        
    except Exception as e:
        print(f"❌ 图像保存功能测试失败: {e}")
        return False


def test_info_display():
    """测试信息显示功能"""
    print("5. 测试信息显示功能...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        visualizer = Visualizer()
        
        # 测试基本信息显示
        outer_rect = (50, 50, 500, 300)
        inner_rect = (100, 100, 400, 200)
        
        # 这个函数主要是打印信息，我们检查它不会抛出异常
        visualizer.display_info(outer_rect, inner_rect)
        
        print("✓ 信息显示测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 信息显示功能测试失败: {e}")
        return False


def test_comparison_view():
    """测试对比视图功能"""
    print("6. 测试对比视图功能...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        # 创建三个测试图像
        original = create_test_image()
        processed = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)  # 灰度图像
        result = original.copy()
        
        # 在结果图像上添加一些标记
        cv2.rectangle(result, (50, 50), (550, 350), (0, 255, 0), 3)
        
        visualizer = Visualizer()
        
        # 测试对比视图创建
        comparison = visualizer.create_comparison_view(original, processed, result)
        
        # 验证结果
        assert comparison is not None, "对比视图结果不应为空"
        assert comparison.shape[1] == original.shape[1] * 3, "对比视图宽度应该是原图的3倍"
        
        print("✓ 对比视图测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 对比视图功能测试失败: {e}")
        return False


def test_edge_cases():
    """测试边界情况"""
    print("7. 测试边界情况...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    visualizer = Visualizer()
    
    try:
        # 测试空图像
        try:
            visualizer.draw_boundaries(None, (0, 0, 100, 100), (10, 10, 80, 80))
            assert False, "应该抛出异常"
        except ValueError:
            print("✓ 空图像异常处理正确")
        
        # 测试空边界
        test_image = create_test_image()
        try:
            visualizer.draw_boundaries(test_image, None, (10, 10, 80, 80))
            assert False, "应该抛出异常"
        except ValueError:
            print("✓ 空边界异常处理正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=== 可视化模块测试 ===")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV环境不可用，将跳过大部分测试")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_basic_visualization())
    test_results.append(test_precise_visualization())
    test_results.append(test_info_overlay())
    test_results.append(test_image_saving())
    test_results.append(test_info_display())
    test_results.append(test_comparison_view())
    test_results.append(test_edge_cases())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("✅ 所有测试通过！")
        if not OPENCV_AVAILABLE:
            print("📝 注意: 由于OpenCV不可用，部分测试被跳过")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
