# OpenCV边框检测系统配置文件
import cv2

# 图像预处理参数
GAUSSIAN_BLUR_KERNEL = (5, 5)  # 高斯模糊核大小
ADAPTIVE_THRESH_BLOCK_SIZE = 11  # 自适应阈值块大小
ADAPTIVE_THRESH_C = 2  # 自适应阈值常数

# 边缘检测参数
CANNY_LOW_THRESHOLD = 50  # Canny边缘检测低阈值
CANNY_HIGH_THRESHOLD = 150  # Canny边缘检测高阈值

# 轮廓筛选参数
MIN_CONTOUR_AREA = 1000  # 最小轮廓面积
CONTOUR_APPROX_EPSILON = 0.02  # 轮廓近似精度

# 坐标计算参数
PIXELS_PER_CM = 28.35  # 像素密度(像素/厘米)，基于72DPI
BORDER_WIDTH_CM = 2  # 边框宽度(厘米)

# 形态学操作参数
MORPH_KERNEL_SIZE = (3, 3)  # 形态学操作核大小
MORPH_KERNEL_TYPE = cv2.MORPH_RECT  # 形态学操作核类型

# 可视化参数
OUTER_BORDER_COLOR = (0, 255, 0)  # 外边界颜色(绿色)
INNER_BORDER_COLOR = (0, 0, 255)  # 内边界颜色(红色)
BORDER_THICKNESS = 2  # 边界线宽度
