#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV边框检测系统主程序
用于识别A4纸上2cm宽黑色边框线的内外边界
"""

import sys
import os

def verify_opencv_installation():
    """验证OpenCV安装"""
    try:
        # 延迟导入避免启动时的依赖问题
        import cv2
        print(f"OpenCV版本: {cv2.__version__}")
        print("OpenCV安装验证成功!")
        return True
    except ImportError as e:
        print(f"OpenCV导入失败: {e}")
        print("提示: 请确保系统中已安装OpenCV")
        return False
    except Exception as e:
        print(f"OpenCV验证失败: {e}")
        print("提示: 可能存在依赖库问题，但项目结构已创建")
        return False

def main():
    """主程序入口"""
    print("=== OpenCV边框检测系统 ===")

    # 检查项目结构
    required_dirs = ['src', 'tests', 'config', 'examples']
    all_dirs_exist = True
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"警告: 缺少目录 {dir_name}")
            all_dirs_exist = False
        else:
            print(f"✓ 目录 {dir_name} 存在")

    # 检查配置文件
    if os.path.exists('config.py'):
        print("✓ 配置文件 config.py 存在")
    else:
        print("警告: 缺少配置文件 config.py")
        all_dirs_exist = False

    # 验证OpenCV安装（非阻塞）
    opencv_ok = verify_opencv_installation()

    if all_dirs_exist:
        print("✓ 项目结构初始化完成!")
        if opencv_ok:
            print("✓ 系统环境验证通过，可以开始开发!")
        else:
            print("⚠ OpenCV验证失败，但项目结构已就绪")
    else:
        print("❌ 项目结构不完整")
        return 1

    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
