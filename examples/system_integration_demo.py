#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统集成使用示例
演示如何使用BorderDetectionSystem进行完整的边框检测
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.border_detection_system import BorderDetectionSystem
    print("OpenCV导入成功，可以运行完整示例")
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"OpenCV导入失败: {e}")
    print("将运行模拟示例")
    OPENCV_AVAILABLE = False


def create_sample_images():
    """创建示例图像"""
    if not OPENCV_AVAILABLE:
        print("创建模拟示例图像...")
        return []
    
    print("创建示例图像...")
    
    # 创建不同类型的测试图像
    images = []
    
    # 1. 标准A4纸图像
    image1 = create_standard_a4()
    images.append(("standard_a4.jpg", image1))
    
    # 2. 带噪声的图像
    image2 = create_noisy_a4()
    images.append(("noisy_a4.jpg", image2))
    
    # 3. 不同DPI的图像
    image3 = create_high_dpi_a4()
    images.append(("high_dpi_a4.jpg", image3))
    
    # 保存示例图像
    saved_paths = []
    for filename, image in images:
        filepath = os.path.join("examples", filename)
        cv2.imwrite(filepath, image)
        saved_paths.append(filepath)
        print(f"✓ 保存示例图像: {filepath}")
    
    return saved_paths


def create_standard_a4():
    """创建标准A4纸图像"""
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 2cm边框
    border_width = int(2 * 28.35)
    cv2.rectangle(image, (0, 0), (width-1, border_width-1), (0, 0, 0), -1)
    cv2.rectangle(image, (0, height-border_width), (width-1, height-1), (0, 0, 0), -1)
    cv2.rectangle(image, (0, 0), (border_width-1, height-1), (0, 0, 0), -1)
    cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), (0, 0, 0), -1)
    
    # 添加内容
    cv2.putText(image, "Standard A4 Document", (150, 200), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "2cm Black Border", (200, 250), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    return image


def create_noisy_a4():
    """创建带噪声的A4纸图像"""
    image = create_standard_a4()
    
    # 添加高斯噪声
    noise = np.random.normal(0, 20, image.shape).astype(np.int16)
    image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # 添加一些干扰元素
    cv2.rectangle(image, (200, 350), (300, 400), (128, 128, 128), -1)
    cv2.circle(image, (500, 400), 20, (64, 64, 64), -1)
    
    return image


def create_high_dpi_a4():
    """创建高DPI的A4纸图像"""
    # 更大尺寸模拟高DPI
    height, width = 900, 1200
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 按150DPI计算边框宽度
    border_width = int(2 * 59.06)  # 约118像素
    cv2.rectangle(image, (0, 0), (width-1, border_width-1), (0, 0, 0), -1)
    cv2.rectangle(image, (0, height-border_width), (width-1, height-1), (0, 0, 0), -1)
    cv2.rectangle(image, (0, 0), (border_width-1, height-1), (0, 0, 0), -1)
    cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), (0, 0, 0), -1)
    
    # 添加内容
    cv2.putText(image, "High DPI A4 Document", (200, 300), 
                cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 3)
    cv2.putText(image, "150 DPI Resolution", (300, 400), 
                cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
    
    return image


def demonstrate_simple_detection():
    """演示简单检测接口"""
    print("\n=== 简单检测接口演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟简单检测:")
        print("1. 创建BorderDetectionSystem实例")
        print("2. 调用detect_boundaries(image_path)")
        print("3. 返回(outer_rect, inner_rect, result_image)")
        print("✓ 简单检测演示完成（模拟）")
        return True
    
    try:
        # 创建示例图像
        sample_paths = create_sample_images()
        if not sample_paths:
            print("❌ 无法创建示例图像")
            return False
        
        # 创建检测系统
        system = BorderDetectionSystem(dpi=72)
        
        # 处理第一张图像
        image_path = sample_paths[0]
        print(f"处理图像: {image_path}")
        
        outer_rect, inner_rect, result_image = system.detect_boundaries(image_path)
        
        if outer_rect is not None:
            print("✓ 检测成功")
            print(f"  - 外边界: {outer_rect}")
            print(f"  - 内边界: {inner_rect}")
            print(f"  - 处理时间: {system.last_processing_time:.3f}秒")
            
            # 保存结果
            result_path = "examples/simple_detection_result.jpg"
            system.visualizer.save_result(result_image, result_path)
            
            return True
        else:
            print("⚠ 未检测到边框")
            return True  # 不算失败
        
    except Exception as e:
        print(f"❌ 简单检测演示失败: {e}")
        return False


def demonstrate_detailed_detection():
    """演示详细检测接口"""
    print("\n=== 详细检测接口演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟详细检测:")
        print("1. 调用detect_boundaries_detailed()")
        print("2. 返回完整的结果字典")
        print("3. 包含边界信息、详细分析、处理时间等")
        print("✓ 详细检测演示完成（模拟）")
        return True
    
    try:
        # 创建示例图像
        sample_paths = create_sample_images()
        if not sample_paths:
            print("❌ 无法创建示例图像")
            return False
        
        # 创建检测系统
        system = BorderDetectionSystem(dpi=72)
        
        # 详细处理
        image_path = sample_paths[0]
        print(f"详细处理图像: {image_path}")
        
        result = system.detect_boundaries_detailed(
            image_path, 
            save_debug=True,
            debug_path="examples/debug"
        )
        
        if result['success']:
            print("✓ 详细检测成功")
            print(f"  - 处理时间: {result['processing_time']:.3f}秒")
            
            # 显示详细信息
            system.visualizer.display_detailed_info(result['detailed_info'])
            
            # 保存结果
            result_path = "examples/detailed_detection_result.jpg"
            system.visualizer.save_result(result['result_image'], result_path)
            
            if result['debug_images']:
                print(f"  - 调试图像: {len(result['debug_images'])}个文件")
            
            return True
        else:
            print(f"❌ 详细检测失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 详细检测演示失败: {e}")
        return False


def demonstrate_batch_processing():
    """演示批量处理"""
    print("\n=== 批量处理演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟批量处理:")
        print("1. 扫描目录中的所有图像文件")
        print("2. 逐个处理并保存结果")
        print("3. 生成处理统计报告")
        print("✓ 批量处理演示完成（模拟）")
        return True
    
    try:
        # 创建示例图像
        sample_paths = create_sample_images()
        if not sample_paths:
            print("❌ 无法创建示例图像")
            return False
        
        # 创建检测系统
        system = BorderDetectionSystem(dpi=72)
        
        # 批量处理
        print(f"批量处理 {len(sample_paths)} 张图像")
        results = system.batch_process(
            sample_paths,
            output_dir="examples/batch_results"
        )
        
        # 分析结果
        successful = sum(1 for r in results if r['success'])
        print(f"✓ 批量处理完成: {successful}/{len(results)} 成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量处理演示失败: {e}")
        return False


def demonstrate_dpi_handling():
    """演示DPI处理"""
    print("\n=== DPI处理演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟DPI处理:")
        print("1. 创建不同DPI的检测系统")
        print("2. 展示像素密度的差异")
        print("3. 处理高DPI图像")
        print("✓ DPI处理演示完成（模拟）")
        return True
    
    try:
        # 创建示例图像
        sample_paths = create_sample_images()
        if not sample_paths:
            print("❌ 无法创建示例图像")
            return False
        
        # 测试不同DPI
        dpi_values = [72, 150]
        
        for dpi in dpi_values:
            print(f"\n--- {dpi}DPI 处理 ---")
            system = BorderDetectionSystem(dpi=dpi)
            
            # 选择合适的测试图像
            if dpi == 150 and len(sample_paths) >= 3:
                test_image = sample_paths[2]  # 高DPI图像
            else:
                test_image = sample_paths[0]  # 标准图像
            
            result = system.detect_boundaries_detailed(test_image)
            
            if result['success']:
                settings = result['detailed_info']['settings']
                print(f"✓ {dpi}DPI处理成功")
                print(f"  - 像素密度: {settings['pixels_per_cm']:.2f}像素/厘米")
                print(f"  - 边框宽度: {settings['border_width_pixels']}像素")
                print(f"  - 处理时间: {result['processing_time']:.3f}秒")
            else:
                print(f"⚠ {dpi}DPI处理未成功")
        
        return True
        
    except Exception as e:
        print(f"❌ DPI处理演示失败: {e}")
        return False


def main():
    """主演示函数"""
    print("=== 系统集成使用示例 ===")
    
    if OPENCV_AVAILABLE:
        print("✓ OpenCV环境可用，运行完整演示")
    else:
        print("⚠ OpenCV环境不可用，运行模拟演示")
    
    # 执行各项演示
    demos = [
        ("简单检测接口", demonstrate_simple_detection),
        ("详细检测接口", demonstrate_detailed_detection),
        ("批量处理", demonstrate_batch_processing),
        ("DPI处理", demonstrate_dpi_handling)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        print(f"\n--- {demo_name} ---")
        try:
            result = demo_func()
            results.append(result)
            if result:
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {e}")
            results.append(False)
    
    # 总结
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== 演示总结 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有演示都成功完成！")
        print("\n📖 系统使用说明:")
        print("1. 简单使用: system.detect_boundaries(image_path)")
        print("2. 详细分析: system.detect_boundaries_detailed(image_path)")
        print("3. 批量处理: system.batch_process(image_paths)")
        print("4. DPI调整: system.set_dpi(new_dpi)")
        print("5. 系统信息: system.print_system_info()")
        
        print("\n📁 命令行使用:")
        print("  python main.py --image sample.jpg --detailed")
        print("  python main.py --batch images/ --output results/")
        print("  python main.py --info")
    else:
        print("⚠ 部分演示未成功，请检查环境配置")
    
    return 0 if success_count == total_count else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
