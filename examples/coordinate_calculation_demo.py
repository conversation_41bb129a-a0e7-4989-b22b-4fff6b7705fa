#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标计算模块使用示例
演示如何使用CoordinateCalculator类计算A4纸边框的内外边界坐标
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.coordinate_calculator import CoordinateCalculator
    from src.border_detector import BorderDetector
    from src.image_processor import ImageProcessor
    print("OpenCV导入成功，可以运行完整示例")
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"OpenCV导入失败: {e}")
    print("将运行模拟示例")
    OPENCV_AVAILABLE = False


def create_sample_contour():
    """创建示例边框轮廓"""
    if not OPENCV_AVAILABLE:
        print("创建模拟轮廓数据...")
        return "模拟轮廓数据"
    
    # 创建一个标准A4纸边框轮廓（800x600像素图像中的边框）
    # 外边界从(50,50)到(750,550)，形成700x500像素的矩形
    contour = np.array([
        [[50, 50]],    # 左上角
        [[750, 50]],   # 右上角
        [[750, 550]],  # 右下角
        [[50, 550]]    # 左下角
    ], dtype=np.int32)
    
    return contour


def demonstrate_basic_calculation():
    """演示基本坐标计算"""
    print("\n=== 基本坐标计算演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟基本坐标计算:")
        print("1. 输入: 边框轮廓坐标")
        print("2. 计算外边界: 轮廓的边界矩形")
        print("3. 计算内边界: 外边界向内收缩2cm")
        print("4. 输出: (x, y, width, height) 格式的矩形坐标")
        print("✓ 基本坐标计算演示完成（模拟）")
        return True
    
    try:
        # 创建示例轮廓
        sample_contour = create_sample_contour()
        
        # 创建坐标计算器（72DPI）
        calculator = CoordinateCalculator(pixels_per_cm=28.35)
        
        # 计算基本边界
        outer_rect, inner_rect = calculator.calculate_boundaries(sample_contour)
        
        print("基本边界计算结果:")
        print(f"✓ 外边界矩形: {outer_rect}")
        print(f"  - 位置: ({outer_rect[0]}, {outer_rect[1]})")
        print(f"  - 尺寸: {outer_rect[2]} x {outer_rect[3]} 像素")
        
        print(f"✓ 内边界矩形: {inner_rect}")
        print(f"  - 位置: ({inner_rect[0]}, {inner_rect[1]})")
        print(f"  - 尺寸: {inner_rect[2]} x {inner_rect[3]} 像素")
        
        # 计算边框宽度
        border_width_pixels = (inner_rect[0] - outer_rect[0])
        border_width_cm = calculator.pixel_to_cm(border_width_pixels)
        print(f"✓ 边框宽度: {border_width_pixels}像素 = {border_width_cm:.2f}厘米")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本坐标计算演示失败: {e}")
        return False


def demonstrate_precise_calculation():
    """演示精确坐标计算"""
    print("\n=== 精确坐标计算演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟精确坐标计算:")
        print("1. 轮廓近似: 将轮廓近似为4个顶点的多边形")
        print("2. 顶点排序: 按左上、右上、右下、左下顺序排列")
        print("3. 内边界计算: 每个顶点向内收缩边框宽度")
        print("4. 输出: 详细的顶点坐标和边界信息")
        print("✓ 精确坐标计算演示完成（模拟）")
        return True
    
    try:
        sample_contour = create_sample_contour()
        calculator = CoordinateCalculator(pixels_per_cm=28.35)
        
        # 计算精确边界
        precise_info = calculator.calculate_precise_boundaries(sample_contour)
        
        print("精确边界计算结果:")
        print("✓ 外边界顶点:")
        for i, vertex in enumerate(precise_info['outer_vertices']):
            corner_names = ['左上', '右上', '右下', '左下']
            print(f"  - {corner_names[i]}: ({vertex[0]}, {vertex[1]})")
        
        print("✓ 内边界顶点:")
        for i, vertex in enumerate(precise_info['inner_vertices']):
            corner_names = ['左上', '右上', '右下', '左下']
            print(f"  - {corner_names[i]}: ({vertex[0]}, {vertex[1]})")
        
        print(f"✓ 边框宽度: {precise_info['border_width_pixels']}像素 = {precise_info['border_width_cm']}厘米")
        print(f"✓ 像素密度: {precise_info['pixels_per_cm']:.2f}像素/厘米")
        
        return True
        
    except Exception as e:
        print(f"❌ 精确坐标计算演示失败: {e}")
        return False


def demonstrate_unit_conversion():
    """演示单位转换"""
    print("\n=== 单位转换演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟单位转换:")
        print("- 像素 ↔ 厘米转换")
        print("- 支持不同DPI设置")
        print("- 自动更新边框宽度")
        print("✓ 单位转换演示完成（模拟）")
        return True
    
    try:
        calculator = CoordinateCalculator()
        
        print("单位转换示例:")
        
        # 像素转厘米示例
        pixel_values = [28, 57, 142, 284]
        print("✓ 像素转厘米:")
        for pixels in pixel_values:
            cm = calculator.pixel_to_cm(pixels)
            print(f"  - {pixels}像素 = {cm:.2f}厘米")
        
        # 厘米转像素示例
        cm_values = [1, 2, 5, 10]
        print("✓ 厘米转像素:")
        for cm in cm_values:
            pixels = calculator.cm_to_pixel(cm)
            print(f"  - {cm}厘米 = {pixels}像素")
        
        return True
        
    except Exception as e:
        print(f"❌ 单位转换演示失败: {e}")
        return False


def demonstrate_dpi_adjustment():
    """演示DPI调整"""
    print("\n=== DPI调整演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟DPI调整:")
        print("- 支持常用DPI: 72, 96, 150, 300")
        print("- 自动计算像素密度")
        print("- 动态更新边框宽度")
        print("✓ DPI调整演示完成（模拟）")
        return True
    
    try:
        calculator = CoordinateCalculator()
        
        print("不同DPI下的2cm边框宽度:")
        
        dpi_values = [72, 96, 150, 300]
        for dpi in dpi_values:
            calculator.set_dpi(dpi)
            border_pixels = calculator.border_width_pixels
            print(f"✓ {dpi}DPI: 2cm = {border_pixels}像素 (密度: {calculator.pixels_per_cm:.2f}像素/厘米)")
        
        # 演示DPI对坐标计算的影响
        sample_contour = create_sample_contour()
        
        print("\n不同DPI下的边界计算:")
        for dpi in [72, 150]:
            calculator.set_dpi(dpi)
            outer_rect, inner_rect = calculator.calculate_boundaries(sample_contour)
            
            outer_width_cm = calculator.pixel_to_cm(outer_rect[2])
            outer_height_cm = calculator.pixel_to_cm(outer_rect[3])
            inner_width_cm = calculator.pixel_to_cm(inner_rect[2])
            inner_height_cm = calculator.pixel_to_cm(inner_rect[3])
            
            print(f"✓ {dpi}DPI:")
            print(f"  - 外边界: {outer_rect[2]}x{outer_rect[3]}像素 = {outer_width_cm:.1f}x{outer_height_cm:.1f}厘米")
            print(f"  - 内边界: {inner_rect[2]}x{inner_rect[3]}像素 = {inner_width_cm:.1f}x{inner_height_cm:.1f}厘米")
        
        return True
        
    except Exception as e:
        print(f"❌ DPI调整演示失败: {e}")
        return False


def demonstrate_detailed_info():
    """演示详细信息获取"""
    print("\n=== 详细信息获取演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟详细信息获取:")
        print("- 边界坐标和顶点信息")
        print("- 面积计算（像素²和cm²）")
        print("- 尺寸信息（像素和厘米）")
        print("- 系统设置参数")
        print("✓ 详细信息获取演示完成（模拟）")
        return True
    
    try:
        sample_contour = create_sample_contour()
        calculator = CoordinateCalculator(pixels_per_cm=28.35)
        
        # 获取详细信息
        info = calculator.get_boundary_info(sample_contour)
        
        print("详细边界信息:")
        
        # 面积信息
        areas = info['areas']
        print("✓ 面积信息:")
        print(f"  - 外边界面积: {areas['outer_pixels']:.0f}像素² = {areas['outer_cm2']:.2f}cm²")
        print(f"  - 内边界面积: {areas['inner_pixels']:.0f}像素² = {areas['inner_cm2']:.2f}cm²")
        print(f"  - 边框面积: {areas['border_pixels']:.0f}像素² = {areas['border_cm2']:.2f}cm²")
        
        # 尺寸信息
        dimensions = info['dimensions']
        print("✓ 尺寸信息:")
        print(f"  - 外边界: {dimensions['outer_width_pixels']}x{dimensions['outer_height_pixels']}像素")
        print(f"           = {dimensions['outer_width_cm']:.1f}x{dimensions['outer_height_cm']:.1f}厘米")
        print(f"  - 内边界: {dimensions['inner_width_pixels']}x{dimensions['inner_height_pixels']}像素")
        print(f"           = {dimensions['inner_width_cm']:.1f}x{dimensions['inner_height_cm']:.1f}厘米")
        
        # 设置信息
        settings = info['settings']
        print("✓ 系统设置:")
        print(f"  - 像素密度: {settings['pixels_per_cm']:.2f}像素/厘米")
        print(f"  - 边框宽度: {settings['border_width_cm']}厘米 = {settings['border_width_pixels']}像素")
        
        return True
        
    except Exception as e:
        print(f"❌ 详细信息获取演示失败: {e}")
        return False


def main():
    """主演示函数"""
    print("=== 坐标计算模块使用示例 ===")
    
    if OPENCV_AVAILABLE:
        print("✓ OpenCV环境可用，运行完整演示")
    else:
        print("⚠ OpenCV环境不可用，运行模拟演示")
    
    # 执行各项演示
    demos = [
        ("基本坐标计算", demonstrate_basic_calculation),
        ("精确坐标计算", demonstrate_precise_calculation),
        ("单位转换", demonstrate_unit_conversion),
        ("DPI调整", demonstrate_dpi_adjustment),
        ("详细信息获取", demonstrate_detailed_info)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        print(f"\n--- {demo_name} ---")
        try:
            result = demo_func()
            results.append(result)
            if result:
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {e}")
            results.append(False)
    
    # 总结
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== 演示总结 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有演示都成功完成！")
        print("\n📖 使用说明:")
        print("1. 创建 CoordinateCalculator 实例，可指定DPI或像素密度")
        print("2. 调用 calculate_boundaries() 获取基本边界矩形")
        print("3. 调用 calculate_precise_boundaries() 获取精确顶点坐标")
        print("4. 使用 pixel_to_cm() 和 cm_to_pixel() 进行单位转换")
        print("5. 使用 set_dpi() 调整不同图像的像素密度")
        print("6. 使用 get_boundary_info() 获取完整的边界分析信息")
    else:
        print("⚠ 部分演示未成功，请检查环境配置")
    
    return 0 if success_count == total_count else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
