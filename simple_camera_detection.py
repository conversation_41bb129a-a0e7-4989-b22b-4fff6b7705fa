#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版摄像头边框检测
避免复杂的NumPy依赖问题
"""

import sys
import time

def check_opencv():
    """检查OpenCV是否可用"""
    try:
        import cv2
        print(f"✓ OpenCV版本: {cv2.__version__}")
        return True, cv2
    except ImportError as e:
        print(f"❌ OpenCV导入失败: {e}")
        return False, None

def simple_border_detection():
    """简化的边框检测"""
    opencv_ok, cv2 = check_opencv()
    
    if not opencv_ok:
        print("无法使用OpenCV，请检查安装")
        return False
    
    print("=== 简化版摄像头边框检测 ===")
    print("尝试打开摄像头...")
    
    # 尝试打开摄像头
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("❌ 无法打开摄像头")
        print("请检查:")
        print("1. 摄像头是否连接")
        print("2. 摄像头权限是否正确")
        print("3. 是否有其他程序在使用摄像头")
        return False
    
    print("✓ 摄像头打开成功")
    
    # 获取摄像头信息
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    
    print(f"摄像头参数: {width}x{height} @ {fps}FPS")
    
    # 设置窗口
    window_name = "Simple Border Detection - Press 'q' to quit"
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    
    print("\n操作说明:")
    print("- 按 'q' 退出")
    print("- 按 's' 保存当前帧")
    print("- 将A4纸边框放在摄像头前")
    
    frame_count = 0
    start_time = time.time()
    
    try:
        while True:
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头帧")
                break
            
            frame_count += 1
            
            # 简单的边框检测（使用基本的OpenCV函数）
            try:
                # 转换为灰度图
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # 高斯模糊
                blurred = cv2.GaussianBlur(gray, (5, 5), 0)
                
                # Canny边缘检测
                edges = cv2.Canny(blurred, 50, 150)
                
                # 查找轮廓
                contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # 寻找最大的矩形轮廓
                best_contour = None
                max_area = 0
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 1000:  # 过滤小轮廓
                        # 近似为多边形
                        epsilon = 0.02 * cv2.arcLength(contour, True)
                        approx = cv2.approxPolyDP(contour, epsilon, True)
                        
                        # 检查是否为4边形（矩形）
                        if len(approx) == 4 and area > max_area:
                            max_area = area
                            best_contour = contour
                
                # 绘制检测结果
                if best_contour is not None:
                    # 获取边界矩形
                    x, y, w, h = cv2.boundingRect(best_contour)
                    
                    # 计算内边界（假设边框宽度为图像宽度的7%）
                    border_width = int(min(w, h) * 0.07)
                    inner_x = x + border_width
                    inner_y = y + border_width
                    inner_w = w - 2 * border_width
                    inner_h = h - 2 * border_width
                    
                    # 绘制外边框（绿色）
                    cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                    
                    # 绘制内边框（红色）
                    if inner_w > 0 and inner_h > 0:
                        cv2.rectangle(frame, (inner_x, inner_y), (inner_x + inner_w, inner_y + inner_h), (0, 0, 255), 2)
                    
                    # 显示检测信息
                    cv2.putText(frame, "Border Detected", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(frame, f"Size: {w}x{h}", (10, 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                else:
                    cv2.putText(frame, "No Border Found", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                
            except Exception as e:
                # 如果检测失败，只显示原始帧
                cv2.putText(frame, f"Detection Error: {str(e)[:30]}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            
            # 添加帧信息
            elapsed_time = time.time() - start_time
            if elapsed_time > 0:
                current_fps = frame_count / elapsed_time
                cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, height - 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            cv2.putText(frame, "Press 'q' to quit, 's' to save", (10, height - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 显示结果
            cv2.imshow(window_name, frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("用户退出检测")
                break
            elif key == ord('s'):
                # 保存当前帧
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"border_detection_{timestamp}.jpg"
                cv2.imwrite(filename, frame)
                print(f"✓ 保存图像: {filename}")
    
    except KeyboardInterrupt:
        print("\n用户中断检测 (Ctrl+C)")
    
    except Exception as e:
        print(f"❌ 运行时错误: {e}")
        return False
    
    finally:
        # 清理资源
        cap.release()
        cv2.destroyAllWindows()
        
        # 显示统计信息
        total_time = time.time() - start_time
        if total_time > 0:
            avg_fps = frame_count / total_time
            print(f"\n=== 检测统计 ===")
            print(f"总帧数: {frame_count}")
            print(f"运行时间: {total_time:.1f}秒")
            print(f"平均FPS: {avg_fps:.1f}")
    
    print("✓ 检测完成")
    return True

def test_basic_opencv():
    """测试基本OpenCV功能"""
    print("=== 测试基本OpenCV功能 ===")
    
    opencv_ok, cv2 = check_opencv()
    if not opencv_ok:
        return False
    
    try:
        # 测试创建简单图像
        import numpy as np
        print("✓ NumPy可用")
        
        # 创建测试图像
        test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
        print("✓ 可以创建图像")
        
        # 测试基本操作
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        print("✓ 颜色转换正常")
        
        edges = cv2.Canny(gray, 50, 150)
        print("✓ Canny边缘检测正常")
        
        return True
        
    except ImportError as e:
        print(f"❌ NumPy导入失败: {e}")
        print("尝试不使用NumPy的基本功能...")
        return False
    except Exception as e:
        print(f"❌ OpenCV功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 简化版摄像头边框检测 ===")
    
    # 测试基本功能
    if not test_basic_opencv():
        print("\n❌ 基本功能测试失败")
        print("建议:")
        print("1. 重新安装NumPy: pip3 install --user numpy")
        print("2. 安装系统依赖: sudo apt install libopenblas-dev")
        print("3. 重新安装OpenCV: pip3 install --user opencv-python")
        return 1
    
    print("\n✓ 基本功能测试通过")
    
    # 运行边框检测
    success = simple_border_detection()
    
    if success:
        print("\n🎉 边框检测运行成功！")
        print("系统可以正常检测A4纸边框并用绿色/红色线条标出")
        return 0
    else:
        print("\n❌ 边框检测运行失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
