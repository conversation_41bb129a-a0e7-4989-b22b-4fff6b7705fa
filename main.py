#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV边框检测系统主程序
用于识别A4纸上2cm宽黑色边框线的内外边界
"""

import sys
import os
import argparse
from pathlib import Path

def verify_opencv_installation():
    """验证OpenCV安装"""
    try:
        # 延迟导入避免启动时的依赖问题
        import cv2
        print(f"OpenCV版本: {cv2.__version__}")
        print("OpenCV安装验证成功!")
        return True
    except ImportError as e:
        print(f"OpenCV导入失败: {e}")
        print("提示: 请确保系统中已安装OpenCV")
        return False
    except Exception as e:
        print(f"OpenCV验证失败: {e}")
        print("提示: 可能存在依赖库问题，但项目结构已创建")
        return False

def import_system():
    """导入边框检测系统"""
    try:
        from src.border_detection_system import BorderDetectionSystem
        return BorderDetectionSystem
    except ImportError as e:
        print(f"系统模块导入失败: {e}")
        print("提示: 请确保所有模块文件存在且依赖已安装")
        return None

def check_project_structure():
    """检查项目结构"""
    required_dirs = ['src', 'tests', 'config', 'examples']
    required_files = ['config.py']

    all_exist = True

    # 检查目录
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✓ 目录 {dir_name} 存在")
        else:
            print(f"❌ 缺少目录 {dir_name}")
            all_exist = False

    # 检查文件
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"✓ 文件 {file_name} 存在")
        else:
            print(f"❌ 缺少文件 {file_name}")
            all_exist = False

    return all_exist

def run_system_test():
    """运行系统测试"""
    print("\n=== 系统测试 ===")

    # 检查项目结构
    if not check_project_structure():
        print("❌ 项目结构测试失败")
        return 1

    # 检查OpenCV
    if not verify_opencv_installation():
        print("❌ OpenCV测试失败")
        return 1

    # 测试系统导入
    BorderDetectionSystem = import_system()
    if BorderDetectionSystem is None:
        print("❌ 系统导入测试失败")
        return 1

    try:
        # 测试系统初始化
        system = BorderDetectionSystem(dpi=72)
        print("✓ 系统初始化测试通过")

        # 显示系统信息
        system.print_system_info()

        print("\n✅ 所有系统测试通过!")
        return 0

    except Exception as e:
        print(f"❌ 系统测试失败: {e}")
        return 1

def run_detection(image_path, dpi=72, detailed=False, save_result=True, output_dir=None):
    """运行边框检测"""
    BorderDetectionSystem = import_system()
    if BorderDetectionSystem is None:
        return False

    try:
        # 创建检测系统
        system = BorderDetectionSystem(dpi=dpi)

        if detailed:
            # 详细检测
            result = system.detect_boundaries_detailed(
                image_path,
                save_debug=True,
                debug_path=output_dir
            )

            if result['success']:
                print("\n=== 检测结果 ===")
                boundaries = result['boundaries']
                print(f"外边界: {boundaries['outer_rect']}")
                print(f"内边界: {boundaries['inner_rect']}")

                # 显示详细信息
                system.visualizer.display_detailed_info(result['detailed_info'])

                # 保存结果图像
                if save_result:
                    if output_dir is None:
                        output_dir = "results"
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)

                    base_name = os.path.splitext(os.path.basename(image_path))[0]
                    output_path = os.path.join(output_dir, f"{base_name}_result.jpg")
                    system.visualizer.save_result(result['result_image'], output_path)

                return True
            else:
                print(f"❌ 检测失败: {result.get('error', '未知错误')}")
                return False
        else:
            # 简单检测
            outer_rect, inner_rect, result_image = system.detect_boundaries(image_path)

            if outer_rect is not None:
                print("\n=== 检测结果 ===")
                system.visualizer.display_info(outer_rect, inner_rect)

                # 保存结果图像
                if save_result:
                    if output_dir is None:
                        output_dir = "results"
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)

                    base_name = os.path.splitext(os.path.basename(image_path))[0]
                    output_path = os.path.join(output_dir, f"{base_name}_result.jpg")
                    system.visualizer.save_result(result_image, output_path)

                return True
            else:
                print("❌ 检测失败")
                return False

    except Exception as e:
        print(f"❌ 运行检测失败: {e}")
        return False

def run_batch_processing(image_dir, dpi=72, output_dir=None):
    """运行批量处理"""
    BorderDetectionSystem = import_system()
    if BorderDetectionSystem is None:
        return False

    try:
        # 查找图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_paths = []

        for ext in image_extensions:
            image_paths.extend(Path(image_dir).glob(f"*{ext}"))
            image_paths.extend(Path(image_dir).glob(f"*{ext.upper()}"))

        if not image_paths:
            print(f"❌ 在目录 {image_dir} 中未找到图像文件")
            return False

        image_paths = [str(p) for p in image_paths]
        print(f"找到 {len(image_paths)} 张图像")

        # 创建检测系统并批量处理
        system = BorderDetectionSystem(dpi=dpi)
        system.batch_process(image_paths, output_dir=output_dir)

        return True

    except Exception as e:
        print(f"❌ 批量处理失败: {e}")
        return False

def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='A4纸边框检测系统')
    parser.add_argument('--image', '-i', help='输入图像文件路径')
    parser.add_argument('--batch', '-b', help='批量处理目录路径')
    parser.add_argument('--dpi', type=int, default=72, help='图像DPI (默认: 72)')
    parser.add_argument('--detailed', '-d', action='store_true', help='详细检测模式')
    parser.add_argument('--output', '-o', help='输出目录路径')
    parser.add_argument('--no-save', action='store_true', help='不保存结果图像')
    parser.add_argument('--info', action='store_true', help='显示系统信息')
    parser.add_argument('--test', action='store_true', help='运行系统测试')

    args = parser.parse_args()

    print("=== OpenCV边框检测系统 ===")

    # 显示系统信息
    if args.info:
        BorderDetectionSystem = import_system()
        if BorderDetectionSystem:
            system = BorderDetectionSystem(dpi=args.dpi)
            system.print_system_info()
        return 0

    # 运行系统测试
    if args.test:
        return run_system_test()

    # 验证OpenCV安装
    if not verify_opencv_installation():
        print("⚠ OpenCV验证失败，某些功能可能无法正常工作")

    # 检查项目结构
    if not check_project_structure():
        print("❌ 项目结构不完整")
        return 1

    # 单张图像处理
    if args.image:
        success = run_detection(
            args.image,
            dpi=args.dpi,
            detailed=args.detailed,
            save_result=not args.no_save,
            output_dir=args.output
        )
        return 0 if success else 1

    # 批量处理
    elif args.batch:
        success = run_batch_processing(
            args.batch,
            dpi=args.dpi,
            output_dir=args.output
        )
        return 0 if success else 1

    # 默认显示帮助信息
    else:
        parser.print_help()
        print("\n示例用法:")
        print("  python main.py --image sample.jpg --detailed")
        print("  python main.py --batch images/ --output results/")
        print("  python main.py --info")
        print("  python main.py --test")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
