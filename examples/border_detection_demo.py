#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
边框检测模块使用示例
演示如何使用BorderDetector类检测A4纸的黑色边框
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.border_detector import BorderDetector
    from src.image_processor import ImageProcessor
    print("OpenCV导入成功，可以运行完整示例")
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"OpenCV导入失败: {e}")
    print("将运行模拟示例")
    OPENCV_AVAILABLE = False


def create_sample_a4_image():
    """创建示例A4纸图像"""
    if not OPENCV_AVAILABLE:
        print("创建模拟A4纸图像数据...")
        return "模拟图像数据"
    
    print("创建示例A4纸图像...")
    
    # A4纸比例 (210x297mm)，使用800x600像素
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 2cm边框 (假设28.35像素/cm)
    border_width = int(2 * 28.35)  # 约57像素
    
    # 绘制黑色边框
    cv2.rectangle(image, (0, 0), (width-1, border_width-1), (0, 0, 0), -1)  # 上
    cv2.rectangle(image, (0, height-border_width), (width-1, height-1), (0, 0, 0), -1)  # 下
    cv2.rectangle(image, (0, 0), (border_width-1, height-1), (0, 0, 0), -1)  # 左
    cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), (0, 0, 0), -1)  # 右
    
    # 添加一些文档内容
    cv2.putText(image, "Sample A4 Document", (150, 200), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "With 2cm Black Border", (120, 250), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    # 添加一些干扰元素
    cv2.rectangle(image, (200, 300), (300, 350), (128, 128, 128), -1)  # 灰色矩形
    cv2.circle(image, (500, 400), 30, (64, 64, 64), -1)  # 深灰色圆形
    
    # 添加轻微噪声
    noise = np.random.normal(0, 15, image.shape).astype(np.int16)
    image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    return image


def demonstrate_complete_pipeline():
    """演示完整的边框检测流水线"""
    print("\n=== 完整边框检测流水线演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟完整流水线:")
        print("1. 图像预处理: 灰度化 -> 模糊 -> 二值化 -> 形态学操作")
        print("2. 边缘检测: Canny算法检测边缘")
        print("3. 轮廓查找: 查找所有外部轮廓")
        print("4. 轮廓筛选: 面积、顶点数、宽高比、凸性检查")
        print("5. 质量评分: 综合评估选择最佳轮廓")
        print("✓ 完整流水线演示完成（模拟）")
        return True
    
    try:
        # 1. 创建示例图像
        original_image = create_sample_a4_image()
        print("✓ 创建示例A4纸图像")
        
        # 2. 图像预处理
        processor = ImageProcessor()
        binary_image = processor.preprocess(original_image)
        print("✓ 图像预处理完成")
        
        # 3. 边框检测
        detector = BorderDetector()
        border_contour = detector.detect_border(binary_image)
        
        if border_contour is not None:
            print("✓ 边框检测成功")
            
            # 4. 获取详细信息
            info = detector.get_contour_info(border_contour)
            print(f"  - 轮廓面积: {info['area']:.0f} 像素²")
            print(f"  - 轮廓周长: {info['perimeter']:.0f} 像素")
            print(f"  - 顶点数量: {info['vertices_count']}")
            print(f"  - 边界矩形: {info['bounding_rect']}")
            print(f"  - 宽高比: {info['aspect_ratio']:.2f}")
            print(f"  - 质量分数: {info['quality_score']:.1f}")
            print(f"  - 是否凸形: {info['is_convex']}")
            
            # 5. 可视化结果
            result_image = original_image.copy()
            cv2.drawContours(result_image, [border_contour], -1, (0, 255, 0), 3)
            
            # 标注顶点
            for i, vertex in enumerate(info['vertices']):
                cv2.circle(result_image, tuple(vertex), 8, (255, 0, 0), -1)
                cv2.putText(result_image, str(i+1), (vertex[0]+10, vertex[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            
            print("✓ 结果可视化完成")
            return True
        else:
            print("❌ 未检测到边框")
            return False
            
    except Exception as e:
        print(f"❌ 完整流水线演示失败: {e}")
        return False


def demonstrate_parameter_tuning():
    """演示参数调优"""
    print("\n=== 参数调优演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("参数调优说明:")
        print("- CANNY_LOW_THRESHOLD/HIGH_THRESHOLD: 控制边缘检测敏感度")
        print("- MIN_CONTOUR_AREA: 过滤小轮廓，避免噪声干扰")
        print("- CONTOUR_APPROX_EPSILON: 控制轮廓近似精度")
        print("- MIN/MAX_ASPECT_RATIO: 限制宽高比范围")
        print("✓ 参数调优演示完成（模拟）")
        return True
    
    try:
        # 创建测试图像
        original_image = create_sample_a4_image()
        processor = ImageProcessor()
        binary_image = processor.preprocess(original_image)
        
        # 测试不同的Canny阈值
        print("测试不同Canny阈值的效果:")
        canny_configs = [
            (30, 100, "低敏感度"),
            (50, 150, "中等敏感度"),
            (70, 200, "高敏感度")
        ]
        
        for low, high, desc in canny_configs:
            detector = BorderDetector()
            detector.canny_low = low
            detector.canny_high = high
            
            border_contour = detector.detect_border(binary_image)
            
            if border_contour is not None:
                info = detector.get_contour_info(border_contour)
                print(f"  - {desc}({low},{high}): 成功，质量分数={info['quality_score']:.1f}")
            else:
                print(f"  - {desc}({low},{high}): 检测失败")
        
        # 测试不同的面积阈值
        print("\n测试不同面积阈值的效果:")
        area_thresholds = [500, 1000, 2000, 5000]
        
        for threshold in area_thresholds:
            detector = BorderDetector()
            detector.min_area = threshold
            
            border_contour = detector.detect_border(binary_image)
            
            if border_contour is not None:
                info = detector.get_contour_info(border_contour)
                print(f"  - 面积阈值{threshold}: 成功，实际面积={info['area']:.0f}")
            else:
                print(f"  - 面积阈值{threshold}: 检测失败")
        
        print("✓ 参数调优演示完成")
        return True
        
    except Exception as e:
        print(f"❌ 参数调优演示失败: {e}")
        return False


def demonstrate_debug_features():
    """演示调试功能"""
    print("\n=== 调试功能演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("调试功能说明:")
        print("- debug_show_detection_steps(): 显示检测的各个步骤")
        print("- get_contour_info(): 获取轮廓的详细信息")
        print("- 可保存中间结果图像进行分析")
        print("✓ 调试功能演示完成（模拟）")
        return True
    
    try:
        # 创建测试数据
        original_image = create_sample_a4_image()
        processor = ImageProcessor()
        binary_image = processor.preprocess(original_image)
        
        detector = BorderDetector()
        
        # 使用调试功能
        debug_results = detector.debug_show_detection_steps(binary_image)
        
        print("调试信息:")
        print(f"  - 检测到轮廓数量: {debug_results['contour_count']}")
        print(f"  - 边框检测结果: {'成功' if debug_results['border_found'] else '失败'}")
        
        if debug_results['border_found']:
            info = detector.get_contour_info(debug_results['border_contour'])
            print(f"  - 最佳轮廓面积: {info['area']:.0f}")
            print(f"  - 最佳轮廓质量分数: {info['quality_score']:.1f}")
        
        # 显示各步骤图像信息
        step_info = {
            'original': '原始二值化图像',
            'edges': 'Canny边缘检测结果',
            'vis_contours': '所有轮廓可视化',
            'vis_result': '最终检测结果'
        }
        
        for key, desc in step_info.items():
            if key in debug_results and debug_results[key] is not None:
                img = debug_results[key]
                print(f"  - {desc}: {img.shape} {img.dtype}")
        
        print("✓ 调试功能演示完成")
        return True
        
    except Exception as e:
        print(f"❌ 调试功能演示失败: {e}")
        return False


def main():
    """主演示函数"""
    print("=== 边框检测模块使用示例 ===")
    
    if OPENCV_AVAILABLE:
        print("✓ OpenCV环境可用，运行完整演示")
    else:
        print("⚠ OpenCV环境不可用，运行模拟演示")
    
    # 执行各项演示
    demos = [
        ("完整检测流水线", demonstrate_complete_pipeline),
        ("参数调优", demonstrate_parameter_tuning),
        ("调试功能", demonstrate_debug_features)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        print(f"\n--- {demo_name} ---")
        try:
            result = demo_func()
            results.append(result)
            if result:
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {e}")
            results.append(False)
    
    # 总结
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== 演示总结 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有演示都成功完成！")
        print("\n📖 使用说明:")
        print("1. 先使用 ImageProcessor 预处理图像")
        print("2. 创建 BorderDetector 实例")
        print("3. 调用 detect_border(binary_image) 检测边框")
        print("4. 使用 get_contour_info() 获取详细信息")
        print("5. 使用 debug_show_detection_steps() 进行调试")
        print("6. 根据需要调整 config.py 中的参数")
    else:
        print("⚠ 部分演示未成功，请检查环境配置")
    
    return 0 if success_count == total_count else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
