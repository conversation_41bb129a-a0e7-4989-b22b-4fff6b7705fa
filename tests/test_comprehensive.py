#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试套件
包含单元测试、集成测试和鲁棒性测试
"""

import sys
import os
import unittest
import tempfile
import shutil
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.border_detection_system import BorderDetectionSystem
    from src.image_processor import ImageProcessor
    from src.border_detector import BorderDetector
    from src.coordinate_calculator import CoordinateCalculator
    from src.visualizer import Visualizer
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"导入失败: {e}")
    OPENCV_AVAILABLE = False


class TestImageGenerator:
    """测试图像生成器"""
    
    @staticmethod
    def create_standard_a4(width=800, height=600, border_width=57):
        """创建标准A4纸图像"""
        if not OPENCV_AVAILABLE:
            return None
        
        image = np.ones((height, width, 3), dtype=np.uint8) * 255
        
        # 绘制黑色边框
        cv2.rectangle(image, (0, 0), (width-1, border_width-1), (0, 0, 0), -1)
        cv2.rectangle(image, (0, height-border_width), (width-1, height-1), (0, 0, 0), -1)
        cv2.rectangle(image, (0, 0), (border_width-1, height-1), (0, 0, 0), -1)
        cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), (0, 0, 0), -1)
        
        return image
    
    @staticmethod
    def create_noisy_image(base_image, noise_level=20):
        """创建带噪声的图像"""
        if not OPENCV_AVAILABLE or base_image is None:
            return None
        
        noise = np.random.normal(0, noise_level, base_image.shape).astype(np.int16)
        noisy = np.clip(base_image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        return noisy
    
    @staticmethod
    def create_low_contrast_image(base_image):
        """创建低对比度图像"""
        if not OPENCV_AVAILABLE or base_image is None:
            return None
        
        # 将黑色边框变为深灰色
        low_contrast = base_image.copy()
        mask = np.all(low_contrast == [0, 0, 0], axis=2)
        low_contrast[mask] = [64, 64, 64]  # 深灰色而非黑色
        return low_contrast
    
    @staticmethod
    def create_rotated_image(base_image, angle=5):
        """创建旋转图像"""
        if not OPENCV_AVAILABLE or base_image is None:
            return None
        
        h, w = base_image.shape[:2]
        center = (w // 2, h // 2)
        matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        rotated = cv2.warpAffine(base_image, matrix, (w, h), 
                                borderValue=(255, 255, 255))
        return rotated


@unittest.skipIf(not OPENCV_AVAILABLE, "OpenCV不可用")
class TestImageProcessor(unittest.TestCase):
    """图像预处理模块单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.processor = ImageProcessor()
        self.test_image = TestImageGenerator.create_standard_a4()
    
    def test_preprocess_normal_image(self):
        """测试正常图像预处理"""
        result = self.processor.preprocess(self.test_image)
        
        self.assertIsNotNone(result)
        self.assertEqual(len(result.shape), 2)  # 应该是灰度图像
        self.assertEqual(result.dtype, np.uint8)
        
        # 检查二值化效果
        unique_values = np.unique(result)
        self.assertLessEqual(len(unique_values), 2)
    
    def test_preprocess_noisy_image(self):
        """测试噪声图像预处理"""
        noisy_image = TestImageGenerator.create_noisy_image(self.test_image)
        result = self.processor.preprocess(noisy_image)
        
        self.assertIsNotNone(result)
        self.assertEqual(result.shape, (self.test_image.shape[0], self.test_image.shape[1]))
    
    def test_preprocess_edge_cases(self):
        """测试边界情况"""
        # 测试空图像
        with self.assertRaises(ValueError):
            self.processor.preprocess(None)
        
        # 测试极小图像
        tiny_image = np.ones((10, 10, 3), dtype=np.uint8) * 128
        result = self.processor.preprocess(tiny_image)
        self.assertIsNotNone(result)


@unittest.skipIf(not OPENCV_AVAILABLE, "OpenCV不可用")
class TestBorderDetector(unittest.TestCase):
    """边框检测模块单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.detector = BorderDetector()
        self.processor = ImageProcessor()
        self.test_image = TestImageGenerator.create_standard_a4()
    
    def test_detect_normal_border(self):
        """测试正常边框检测"""
        binary = self.processor.preprocess(self.test_image)
        contour = self.detector.detect_border(binary)
        
        if contour is not None:
            # 验证轮廓有效性
            area = cv2.contourArea(contour)
            self.assertGreater(area, 1000)
            
            # 获取轮廓信息
            info = self.detector.get_contour_info(contour)
            self.assertIsNotNone(info)
            self.assertIn('area', info)
            self.assertIn('vertices_count', info)
    
    def test_detect_no_border(self):
        """测试无边框图像"""
        # 创建无边框的纯白图像
        white_image = np.ones((400, 600), dtype=np.uint8) * 255
        contour = self.detector.detect_border(white_image)
        
        self.assertIsNone(contour)
    
    def test_detect_edge_cases(self):
        """测试边界情况"""
        # 测试空图像
        with self.assertRaises(ValueError):
            self.detector.detect_border(None)
        
        # 测试非灰度图像
        color_image = np.ones((100, 100, 3), dtype=np.uint8)
        with self.assertRaises(ValueError):
            self.detector.detect_border(color_image)


@unittest.skipIf(not OPENCV_AVAILABLE, "OpenCV不可用")
class TestCoordinateCalculator(unittest.TestCase):
    """坐标计算模块单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.calculator = CoordinateCalculator(pixels_per_cm=28.35)
        
        # 创建测试轮廓
        self.test_contour = np.array([
            [[50, 50]], [[750, 50]], [[750, 550]], [[50, 550]]
        ], dtype=np.int32)
    
    def test_calculate_boundaries(self):
        """测试边界计算"""
        outer_rect, inner_rect = self.calculator.calculate_boundaries(self.test_contour)
        
        self.assertIsNotNone(outer_rect)
        self.assertIsNotNone(inner_rect)
        self.assertEqual(len(outer_rect), 4)
        self.assertEqual(len(inner_rect), 4)
        
        # 验证内边界在外边界内部
        self.assertGreater(inner_rect[0], outer_rect[0])
        self.assertGreater(inner_rect[1], outer_rect[1])
        self.assertLess(inner_rect[2], outer_rect[2])
        self.assertLess(inner_rect[3], outer_rect[3])
    
    def test_unit_conversion(self):
        """测试单位转换"""
        # 测试像素转厘米
        cm_value = self.calculator.pixel_to_cm(28.35)
        self.assertAlmostEqual(cm_value, 1.0, places=2)
        
        # 测试厘米转像素
        pixel_value = self.calculator.cm_to_pixel(2.0)
        self.assertAlmostEqual(pixel_value, 56.7, places=0)
    
    def test_dpi_settings(self):
        """测试DPI设置"""
        original_ppcm = self.calculator.pixels_per_cm
        
        self.calculator.set_dpi(150)
        new_ppcm = self.calculator.pixels_per_cm
        
        self.assertNotEqual(new_ppcm, original_ppcm)
        self.assertGreater(new_ppcm, original_ppcm)


@unittest.skipIf(not OPENCV_AVAILABLE, "OpenCV不可用")
class TestVisualizer(unittest.TestCase):
    """可视化模块单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.visualizer = Visualizer()
        self.test_image = TestImageGenerator.create_standard_a4()
        self.outer_rect = (50, 50, 700, 500)
        self.inner_rect = (107, 107, 586, 386)
    
    def test_draw_boundaries(self):
        """测试边界绘制"""
        result = self.visualizer.draw_boundaries(
            self.test_image, self.outer_rect, self.inner_rect
        )
        
        self.assertIsNotNone(result)
        self.assertEqual(result.shape, self.test_image.shape)
        self.assertFalse(np.array_equal(result, self.test_image))
    
    def test_add_info_overlay(self):
        """测试信息覆盖层"""
        info_dict = {
            "状态": "成功",
            "外边界": str(self.outer_rect),
            "内边界": str(self.inner_rect)
        }
        
        result = self.visualizer.add_info_overlay(self.test_image, info_dict)
        
        self.assertIsNotNone(result)
        self.assertEqual(result.shape, self.test_image.shape)
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空图像
        with self.assertRaises(ValueError):
            self.visualizer.draw_boundaries(None, self.outer_rect, self.inner_rect)
        
        # 测试空边界
        with self.assertRaises(ValueError):
            self.visualizer.draw_boundaries(self.test_image, None, self.inner_rect)


@unittest.skipIf(not OPENCV_AVAILABLE, "OpenCV不可用")
class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.system = BorderDetectionSystem(dpi=72)
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试图像并保存
        self.test_images = {}
        test_cases = [
            ('normal', TestImageGenerator.create_standard_a4()),
            ('noisy', TestImageGenerator.create_noisy_image(
                TestImageGenerator.create_standard_a4())),
            ('low_contrast', TestImageGenerator.create_low_contrast_image(
                TestImageGenerator.create_standard_a4())),
            ('rotated', TestImageGenerator.create_rotated_image(
                TestImageGenerator.create_standard_a4()))
        ]
        
        for name, image in test_cases:
            if image is not None:
                path = os.path.join(self.temp_dir, f"{name}.jpg")
                cv2.imwrite(path, image)
                self.test_images[name] = path
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def validate_boundaries(self, outer, inner):
        """验证边界有效性"""
        if outer is None or inner is None:
            return False
        
        return (inner[0] > outer[0] and inner[1] > outer[1] and 
                inner[0] + inner[2] < outer[0] + outer[2] and 
                inner[1] + inner[3] < outer[1] + outer[3])
    
    def test_normal_detection(self):
        """测试正常检测"""
        if 'normal' not in self.test_images:
            self.skipTest("无法创建正常测试图像")
        
        outer, inner, result = self.system.detect_boundaries(self.test_images['normal'])
        
        self.assertIsNotNone(outer)
        self.assertIsNotNone(inner)
        self.assertIsNotNone(result)
        self.assertTrue(self.validate_boundaries(outer, inner))
    
    def test_detailed_detection(self):
        """测试详细检测"""
        if 'normal' not in self.test_images:
            self.skipTest("无法创建正常测试图像")
        
        result = self.system.detect_boundaries_detailed(self.test_images['normal'])
        
        self.assertIsInstance(result, dict)
        self.assertIn('success', result)
        self.assertIn('processing_time', result)
        
        if result['success']:
            self.assertIn('boundaries', result)
            self.assertIn('detailed_info', result)
            self.assertIn('result_image', result)
    
    def test_robustness(self):
        """测试系统鲁棒性"""
        success_count = 0
        total_count = 0
        
        for name, path in self.test_images.items():
            total_count += 1
            outer, inner, result = self.system.detect_boundaries(path)
            
            if outer is not None and inner is not None:
                success_count += 1
                self.assertTrue(self.validate_boundaries(outer, inner))
        
        # 至少50%的测试图像应该检测成功
        success_rate = success_count / total_count if total_count > 0 else 0
        self.assertGreaterEqual(success_rate, 0.5, 
                               f"检测成功率过低: {success_rate:.2%}")
    
    def test_batch_processing(self):
        """测试批量处理"""
        if not self.test_images:
            self.skipTest("无测试图像可用")
        
        image_paths = list(self.test_images.values())
        results = self.system.batch_process(image_paths, save_results=False)
        
        self.assertEqual(len(results), len(image_paths))
        
        for result in results:
            self.assertIsInstance(result, dict)
            self.assertIn('success', result)
            self.assertIn('processing_time', result)


class TestSystemWithoutOpenCV(unittest.TestCase):
    """无OpenCV环境的系统测试"""
    
    def test_import_structure(self):
        """测试模块导入结构"""
        # 测试主要模块文件是否存在
        module_files = [
            'src/image_processor.py',
            'src/border_detector.py', 
            'src/coordinate_calculator.py',
            'src/visualizer.py',
            'src/border_detection_system.py'
        ]
        
        for module_file in module_files:
            self.assertTrue(os.path.exists(module_file), 
                           f"模块文件不存在: {module_file}")
    
    def test_config_file(self):
        """测试配置文件"""
        self.assertTrue(os.path.exists('config.py'))
        
        # 读取配置文件内容
        with open('config.py', 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        # 检查关键配置参数
        required_params = [
            'PIXELS_PER_CM', 'BORDER_WIDTH_CM', 
            'OUTER_BORDER_COLOR', 'INNER_BORDER_COLOR'
        ]
        
        for param in required_params:
            self.assertIn(param, config_content, 
                         f"配置参数缺失: {param}")


def run_comprehensive_tests():
    """运行综合测试"""
    print("=== 综合测试套件 ===")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestSystemWithoutOpenCV,  # 总是运行
    ]
    
    if OPENCV_AVAILABLE:
        test_classes.extend([
            TestImageProcessor,
            TestBorderDetector,
            TestCoordinateCalculator,
            TestVisualizer,
            TestSystemIntegration
        ])
        print("✓ OpenCV可用，运行完整测试套件")
    else:
        print("⚠ OpenCV不可用，运行基础结构测试")
    
    for test_class in test_classes:
        suite.addTests(loader.loadTestsFromTestCase(test_class))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful(), result.testsRun, len(result.failures), len(result.errors)


if __name__ == "__main__":
    success, total, failures, errors = run_comprehensive_tests()
    
    print(f"\n=== 测试结果摘要 ===")
    print(f"总测试数: {total}")
    print(f"失败数: {failures}")
    print(f"错误数: {errors}")
    print(f"成功率: {((total-failures-errors)/total*100):.1f}%" if total > 0 else "0%")
    
    if success:
        print("✅ 所有测试通过！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败")
        sys.exit(1)
