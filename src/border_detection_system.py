#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主系统集成模块
整合所有模块，提供统一的边框检测API接口
"""

import sys
import os
import time
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from config import *
    from src.image_processor import ImageProcessor
    from src.border_detector import BorderDetector
    from src.coordinate_calculator import CoordinateCalculator
    from src.visualizer import Visualizer
except ImportError as e:
    print(f"导入依赖失败: {e}")
    print("请确保已安装OpenCV和NumPy，并且所有模块文件存在")
    sys.exit(1)


class BorderDetectionSystem:
    """边框检测系统主类，整合所有功能模块"""
    
    def __init__(self, pixels_per_cm=None, dpi=None):
        """
        初始化边框检测系统
        Args:
            pixels_per_cm: 像素密度(像素/厘米)，优先级高于dpi
            dpi: 图像DPI，如果未提供pixels_per_cm则根据dpi计算
        """
        # 初始化各个模块
        self.processor = ImageProcessor()
        self.detector = BorderDetector()
        self.calculator = CoordinateCalculator(pixels_per_cm=pixels_per_cm, dpi=dpi)
        self.visualizer = Visualizer()
        
        # 系统状态
        self.last_processing_time = 0
        self.last_result = None
        
        print("✓ 边框检测系统初始化完成")
        print(f"  - 像素密度: {self.calculator.pixels_per_cm:.2f} 像素/厘米")
        print(f"  - 边框宽度: {self.calculator.border_width_cm}厘米 = {self.calculator.border_width_pixels}像素")
    
    def detect_boundaries(self, image_path):
        """
        检测图像中的边框边界（简化接口）
        Args:
            image_path: 图像文件路径
        Returns:
            tuple: (outer_rect, inner_rect, result_image) 或 (None, None, None)
        """
        try:
            start_time = time.time()
            
            # 1. 读取图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法读取图像: {image_path}")
                return None, None, None
            
            print(f"✓ 读取图像: {image.shape}")
            
            # 2. 图像预处理
            binary = self.processor.preprocess(image)
            print("✓ 图像预处理完成")
            
            # 3. 边框检测
            contour = self.detector.detect_border(binary)
            if contour is None:
                print("❌ 未检测到边框")
                return None, None, None
            
            print("✓ 边框检测成功")
            
            # 4. 坐标计算
            outer_rect, inner_rect = self.calculator.calculate_boundaries(contour)
            print("✓ 坐标计算完成")
            
            # 5. 结果可视化
            result_image = self.visualizer.draw_boundaries(image, outer_rect, inner_rect)
            print("✓ 结果可视化完成")
            
            # 记录处理时间
            self.last_processing_time = time.time() - start_time
            print(f"✓ 处理完成，耗时: {self.last_processing_time:.3f}秒")
            
            return outer_rect, inner_rect, result_image
            
        except Exception as e:
            print(f"❌ 边框检测失败: {e}")
            return None, None, None
    
    def detect_boundaries_detailed(self, image_path, save_debug=False, debug_path=None):
        """
        检测图像中的边框边界（详细接口）
        Args:
            image_path: 图像文件路径
            save_debug: 是否保存调试图像
            debug_path: 调试图像保存路径
        Returns:
            dict: 详细的检测结果信息
        """
        try:
            start_time = time.time()
            result = {
                'success': False,
                'image_path': image_path,
                'processing_time': 0,
                'boundaries': None,
                'detailed_info': None,
                'result_image': None,
                'debug_images': []
            }
            
            # 1. 读取图像
            image = cv2.imread(image_path)
            if image is None:
                result['error'] = f"无法读取图像: {image_path}"
                return result
            
            print(f"✓ 读取图像: {image.shape}")
            
            # 2. 图像预处理
            binary = self.processor.preprocess(image)
            print("✓ 图像预处理完成")
            
            # 3. 边框检测
            contour = self.detector.detect_border(binary)
            if contour is None:
                result['error'] = "未检测到边框"
                return result
            
            print("✓ 边框检测成功")
            
            # 4. 详细坐标计算
            detailed_boundaries = self.calculator.calculate_precise_boundaries(contour)
            detailed_info = self.calculator.get_boundary_info(contour)
            print("✓ 详细坐标计算完成")
            
            # 5. 结果可视化
            outer_vertices = detailed_boundaries['outer_vertices']
            inner_vertices = detailed_boundaries['inner_vertices']
            result_image = self.visualizer.draw_precise_boundaries(image, outer_vertices, inner_vertices)
            
            # 添加信息覆盖层
            info_dict = {
                "检测状态": "成功",
                "外边界": f"{detailed_boundaries['outer_rect']}",
                "内边界": f"{detailed_boundaries['inner_rect']}",
                "边框宽度": f"{detailed_boundaries['border_width_pixels']}像素",
                "像素密度": f"{detailed_boundaries['pixels_per_cm']:.1f}px/cm"
            }
            result_image = self.visualizer.add_info_overlay(result_image, info_dict)
            print("✓ 结果可视化完成")
            
            # 6. 保存调试图像（可选）
            if save_debug:
                debug_data = {
                    'original': image,
                    'binary': binary,
                    'result': result_image
                }
                
                if debug_path is None:
                    debug_path = f"debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                saved_files = self.visualizer.save_debug_images(debug_data, debug_path)
                result['debug_images'] = saved_files
                print(f"✓ 调试图像已保存: {len(saved_files)}个文件")
            
            # 7. 组装结果
            processing_time = time.time() - start_time
            result.update({
                'success': True,
                'processing_time': processing_time,
                'boundaries': detailed_boundaries,
                'detailed_info': detailed_info,
                'result_image': result_image
            })
            
            self.last_processing_time = processing_time
            self.last_result = result
            
            print(f"✓ 详细检测完成，耗时: {processing_time:.3f}秒")
            return result
            
        except Exception as e:
            result['error'] = str(e)
            result['processing_time'] = time.time() - start_time
            print(f"❌ 详细边框检测失败: {e}")
            return result
    
    def batch_process(self, image_paths, output_dir=None, save_results=True):
        """
        批量处理图像
        Args:
            image_paths: 图像文件路径列表
            output_dir: 输出目录
            save_results: 是否保存结果图像
        Returns:
            list: 批量处理结果列表
        """
        if output_dir is None:
            output_dir = f"batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if save_results and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        results = []
        total_images = len(image_paths)
        
        print(f"=== 开始批量处理 {total_images} 张图像 ===")
        
        for i, image_path in enumerate(image_paths, 1):
            print(f"\n[{i}/{total_images}] 处理: {os.path.basename(image_path)}")
            
            # 处理单张图像
            result = self.detect_boundaries_detailed(image_path)
            
            # 保存结果图像
            if save_results and result['success']:
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_path = os.path.join(output_dir, f"{base_name}_result.jpg")
                
                success = self.visualizer.save_result(result['result_image'], output_path)
                if success:
                    result['output_path'] = output_path
            
            results.append(result)
        
        # 统计结果
        successful = sum(1 for r in results if r['success'])
        failed = total_images - successful
        total_time = sum(r['processing_time'] for r in results)
        
        print(f"\n=== 批量处理完成 ===")
        print(f"成功: {successful}/{total_images}")
        print(f"失败: {failed}/{total_images}")
        print(f"总耗时: {total_time:.3f}秒")
        print(f"平均耗时: {total_time/total_images:.3f}秒/张")
        
        if save_results:
            print(f"结果保存到: {output_dir}")
        
        return results
    
    def set_dpi(self, dpi):
        """
        设置图像DPI
        Args:
            dpi: 新的DPI值
        """
        self.calculator.set_dpi(dpi)
        print(f"✓ DPI已更新为: {dpi}")
        print(f"  - 像素密度: {self.calculator.pixels_per_cm:.2f} 像素/厘米")
        print(f"  - 边框宽度: {self.calculator.border_width_pixels}像素")
    
    def get_system_info(self):
        """
        获取系统信息
        Returns:
            dict: 系统配置信息
        """
        info = {
            'version': '1.0.0',
            'modules': {
                'image_processor': 'ImageProcessor',
                'border_detector': 'BorderDetector', 
                'coordinate_calculator': 'CoordinateCalculator',
                'visualizer': 'Visualizer'
            },
            'settings': {
                'pixels_per_cm': self.calculator.pixels_per_cm,
                'border_width_cm': self.calculator.border_width_cm,
                'border_width_pixels': self.calculator.border_width_pixels,
                'outer_border_color': OUTER_BORDER_COLOR,
                'inner_border_color': INNER_BORDER_COLOR
            },
            'last_processing_time': self.last_processing_time,
            'opencv_version': cv2.__version__
        }
        return info
    
    def print_system_info(self):
        """打印系统信息"""
        info = self.get_system_info()
        
        print("=== 边框检测系统信息 ===")
        print(f"版本: {info['version']}")
        print(f"OpenCV版本: {info['opencv_version']}")
        
        print("\n模块组件:")
        for module, class_name in info['modules'].items():
            print(f"  - {module}: {class_name}")
        
        print("\n系统设置:")
        settings = info['settings']
        print(f"  - 像素密度: {settings['pixels_per_cm']:.2f} 像素/厘米")
        print(f"  - 边框宽度: {settings['border_width_cm']}厘米 = {settings['border_width_pixels']}像素")
        print(f"  - 外边界颜色: {settings['outer_border_color']}")
        print(f"  - 内边界颜色: {settings['inner_border_color']}")
        
        if self.last_processing_time > 0:
            print(f"\n上次处理耗时: {self.last_processing_time:.3f}秒")


def test_border_detection_system():
    """测试边框检测系统"""
    print("=== 边框检测系统测试 ===")
    
    try:
        # 创建系统实例
        system = BorderDetectionSystem(dpi=72)
        
        # 打印系统信息
        system.print_system_info()
        
        print("\n✓ 边框检测系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 边框检测系统测试失败: {e}")
        return False


if __name__ == "__main__":
    test_border_detection_system()
