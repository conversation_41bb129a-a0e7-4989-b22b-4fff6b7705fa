#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图像生成器
生成各种测试条件下的A4纸边框图像
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"OpenCV导入失败: {e}")
    OPENCV_AVAILABLE = False


class TestImageGenerator:
    """测试图像生成器"""
    
    def __init__(self, output_dir="test_images"):
        """
        初始化生成器
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 标准参数
        self.standard_size = (800, 600)
        self.border_width = int(2 * 28.35)  # 2cm对应像素
        
        print(f"✓ 测试图像生成器初始化完成")
        print(f"  输出目录: {output_dir}")
        print(f"  标准尺寸: {self.standard_size}")
        print(f"  边框宽度: {self.border_width}像素")
    
    def create_base_a4_image(self, width=800, height=600, border_width=None):
        """
        创建基础A4纸图像
        Args:
            width: 图像宽度
            height: 图像高度
            border_width: 边框宽度
        Returns:
            image: 生成的图像
        """
        if not OPENCV_AVAILABLE:
            return None
        
        if border_width is None:
            border_width = self.border_width
        
        # 创建白色背景
        image = np.ones((height, width, 3), dtype=np.uint8) * 255
        
        # 绘制黑色边框
        cv2.rectangle(image, (0, 0), (width-1, border_width-1), (0, 0, 0), -1)  # 上
        cv2.rectangle(image, (0, height-border_width), (width-1, height-1), (0, 0, 0), -1)  # 下
        cv2.rectangle(image, (0, 0), (border_width-1, height-1), (0, 0, 0), -1)  # 左
        cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), (0, 0, 0), -1)  # 右
        
        # 添加文档内容
        cv2.putText(image, "Test A4 Document", (width//4, height//2), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(image, f"{width}x{height}", (width//4, height//2 + 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (100, 100, 100), 2)
        
        return image
    
    def generate_standard_images(self):
        """生成标准测试图像"""
        print("\n=== 生成标准测试图像 ===")
        
        if not OPENCV_AVAILABLE:
            print("⚠ OpenCV不可用，跳过图像生成")
            return []
        
        generated_files = []
        
        # 1. 标准A4图像
        standard_image = self.create_base_a4_image()
        filename = os.path.join(self.output_dir, "standard_a4.jpg")
        cv2.imwrite(filename, standard_image)
        generated_files.append(filename)
        print(f"✓ 生成标准A4图像: {filename}")
        
        # 2. 不同尺寸的图像
        sizes = [
            (400, 300, "small"),
            (1280, 960, "large"),
            (1920, 1440, "xlarge")
        ]
        
        for width, height, size_name in sizes:
            border_w = int(min(width, height) * 0.07)  # 按比例计算边框
            image = self.create_base_a4_image(width, height, border_w)
            filename = os.path.join(self.output_dir, f"a4_{size_name}_{width}x{height}.jpg")
            cv2.imwrite(filename, image)
            generated_files.append(filename)
            print(f"✓ 生成{size_name}尺寸图像: {filename}")
        
        # 3. 不同DPI对应的边框宽度
        dpi_tests = [
            (72, 28.35, "72dpi"),
            (96, 37.8, "96dpi"),
            (150, 59.06, "150dpi"),
            (300, 118.11, "300dpi")
        ]
        
        for dpi, pixels_per_cm, dpi_name in dpi_tests:
            border_w = int(2 * pixels_per_cm)
            image = self.create_base_a4_image(border_width=border_w)
            filename = os.path.join(self.output_dir, f"a4_{dpi_name}.jpg")
            cv2.imwrite(filename, image)
            generated_files.append(filename)
            print(f"✓ 生成{dpi}DPI图像: {filename}")
        
        return generated_files
    
    def generate_challenging_images(self):
        """生成挑战性测试图像"""
        print("\n=== 生成挑战性测试图像 ===")
        
        if not OPENCV_AVAILABLE:
            print("⚠ OpenCV不可用，跳过图像生成")
            return []
        
        generated_files = []
        base_image = self.create_base_a4_image()
        
        # 1. 噪声图像
        noise_levels = [10, 30, 50]
        for noise_level in noise_levels:
            noisy_image = self._add_noise(base_image, noise_level)
            filename = os.path.join(self.output_dir, f"noisy_level_{noise_level}.jpg")
            cv2.imwrite(filename, noisy_image)
            generated_files.append(filename)
            print(f"✓ 生成噪声图像(级别{noise_level}): {filename}")
        
        # 2. 低对比度图像
        contrast_levels = [64, 128, 192]  # 黑色边框的灰度值
        for i, gray_level in enumerate(contrast_levels):
            low_contrast = base_image.copy()
            mask = np.all(low_contrast == [0, 0, 0], axis=2)
            low_contrast[mask] = [gray_level, gray_level, gray_level]
            filename = os.path.join(self.output_dir, f"low_contrast_{i+1}.jpg")
            cv2.imwrite(filename, low_contrast)
            generated_files.append(filename)
            print(f"✓ 生成低对比度图像{i+1}: {filename}")
        
        # 3. 模糊图像
        blur_kernels = [(5, 5), (11, 11), (21, 21)]
        for i, kernel in enumerate(blur_kernels):
            blurred = cv2.GaussianBlur(base_image, kernel, 0)
            filename = os.path.join(self.output_dir, f"blurred_{i+1}.jpg")
            cv2.imwrite(filename, blurred)
            generated_files.append(filename)
            print(f"✓ 生成模糊图像{i+1}: {filename}")
        
        # 4. 旋转图像
        angles = [2, 5, 10, 15]
        for angle in angles:
            rotated = self._rotate_image(base_image, angle)
            filename = os.path.join(self.output_dir, f"rotated_{angle}deg.jpg")
            cv2.imwrite(filename, rotated)
            generated_files.append(filename)
            print(f"✓ 生成旋转图像({angle}度): {filename}")
        
        # 5. 光照不均图像
        lighting_conditions = ["left_shadow", "right_shadow", "center_bright"]
        for condition in lighting_conditions:
            lit_image = self._apply_lighting(base_image, condition)
            filename = os.path.join(self.output_dir, f"lighting_{condition}.jpg")
            cv2.imwrite(filename, lit_image)
            generated_files.append(filename)
            print(f"✓ 生成光照图像({condition}): {filename}")
        
        return generated_files
    
    def generate_edge_case_images(self):
        """生成边界情况测试图像"""
        print("\n=== 生成边界情况测试图像 ===")
        
        if not OPENCV_AVAILABLE:
            print("⚠ OpenCV不可用，跳过图像生成")
            return []
        
        generated_files = []
        
        # 1. 无边框图像
        no_border = np.ones((600, 800, 3), dtype=np.uint8) * 255
        cv2.putText(no_border, "No Border Document", (200, 300), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        filename = os.path.join(self.output_dir, "no_border.jpg")
        cv2.imwrite(filename, no_border)
        generated_files.append(filename)
        print(f"✓ 生成无边框图像: {filename}")
        
        # 2. 不完整边框图像
        incomplete_border = self.create_base_a4_image()
        # 移除部分边框
        cv2.rectangle(incomplete_border, (100, 0), (200, self.border_width), (255, 255, 255), -1)
        filename = os.path.join(self.output_dir, "incomplete_border.jpg")
        cv2.imwrite(filename, incomplete_border)
        generated_files.append(filename)
        print(f"✓ 生成不完整边框图像: {filename}")
        
        # 3. 多重边框图像
        multi_border = self.create_base_a4_image()
        # 添加内部边框
        inner_border = self.border_width + 20
        cv2.rectangle(multi_border, (inner_border, inner_border), 
                     (800-inner_border, 600-inner_border), (0, 0, 0), 3)
        filename = os.path.join(self.output_dir, "multi_border.jpg")
        cv2.imwrite(filename, multi_border)
        generated_files.append(filename)
        print(f"✓ 生成多重边框图像: {filename}")
        
        # 4. 极小图像
        tiny_image = self.create_base_a4_image(100, 80, 10)
        filename = os.path.join(self.output_dir, "tiny_100x80.jpg")
        cv2.imwrite(filename, tiny_image)
        generated_files.append(filename)
        print(f"✓ 生成极小图像: {filename}")
        
        # 5. 纯黑图像
        black_image = np.zeros((600, 800, 3), dtype=np.uint8)
        filename = os.path.join(self.output_dir, "pure_black.jpg")
        cv2.imwrite(filename, black_image)
        generated_files.append(filename)
        print(f"✓ 生成纯黑图像: {filename}")
        
        # 6. 纯白图像
        white_image = np.ones((600, 800, 3), dtype=np.uint8) * 255
        filename = os.path.join(self.output_dir, "pure_white.jpg")
        cv2.imwrite(filename, white_image)
        generated_files.append(filename)
        print(f"✓ 生成纯白图像: {filename}")
        
        return generated_files
    
    def _add_noise(self, image, noise_level):
        """添加高斯噪声"""
        noise = np.random.normal(0, noise_level, image.shape).astype(np.int16)
        return np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    def _rotate_image(self, image, angle):
        """旋转图像"""
        h, w = image.shape[:2]
        center = (w // 2, h // 2)
        matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        return cv2.warpAffine(image, matrix, (w, h), borderValue=(255, 255, 255))
    
    def _apply_lighting(self, image, condition):
        """应用光照效果"""
        h, w = image.shape[:2]
        lighting = np.ones((h, w), dtype=np.float32)
        
        if condition == "left_shadow":
            for x in range(w):
                lighting[:, x] = 0.3 + 0.7 * (x / w)
        elif condition == "right_shadow":
            for x in range(w):
                lighting[:, x] = 1.0 - 0.7 * (x / w)
        elif condition == "center_bright":
            center_x = w // 2
            for x in range(w):
                dist = abs(x - center_x) / center_x
                lighting[:, x] = 0.5 + 0.5 * (1 - dist)
        
        # 应用光照
        result = image.copy().astype(np.float32)
        for c in range(3):
            result[:, :, c] *= lighting
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def generate_all_test_images(self):
        """生成所有测试图像"""
        print("=== 生成完整测试图像集 ===")
        
        all_files = []
        
        # 生成各类测试图像
        all_files.extend(self.generate_standard_images())
        all_files.extend(self.generate_challenging_images())
        all_files.extend(self.generate_edge_case_images())
        
        print(f"\n=== 生成完成 ===")
        print(f"总共生成: {len(all_files)} 张测试图像")
        print(f"保存位置: {self.output_dir}")
        
        # 创建图像清单
        manifest_path = os.path.join(self.output_dir, "image_manifest.txt")
        with open(manifest_path, 'w', encoding='utf-8') as f:
            f.write("测试图像清单\n")
            f.write("=" * 50 + "\n\n")
            
            categories = {
                "标准图像": [f for f in all_files if any(x in f for x in ["standard", "small", "large", "dpi"])],
                "挑战性图像": [f for f in all_files if any(x in f for x in ["noisy", "contrast", "blur", "rotated", "lighting"])],
                "边界情况": [f for f in all_files if any(x in f for x in ["no_border", "incomplete", "multi", "tiny", "pure"])]
            }
            
            for category, files in categories.items():
                f.write(f"{category}:\n")
                for file_path in files:
                    filename = os.path.basename(file_path)
                    f.write(f"  - {filename}\n")
                f.write("\n")
        
        print(f"✓ 图像清单已保存: {manifest_path}")
        
        return all_files


def main():
    """主函数"""
    print("=== 测试图像生成器 ===")
    
    if not OPENCV_AVAILABLE:
        print("❌ OpenCV不可用，无法生成测试图像")
        return 1
    
    # 创建生成器
    generator = TestImageGenerator()
    
    # 生成所有测试图像
    generated_files = generator.generate_all_test_images()
    
    if generated_files:
        print(f"\n✅ 成功生成 {len(generated_files)} 张测试图像")
        print("这些图像可用于:")
        print("  - 单元测试和集成测试")
        print("  - 性能测试和压力测试")
        print("  - 鲁棒性验证")
        print("  - 边界情况测试")
        return 0
    else:
        print("❌ 未能生成测试图像")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
