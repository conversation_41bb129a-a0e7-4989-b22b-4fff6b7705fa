#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预处理模块使用示例
演示如何使用ImageProcessor类处理A4纸黑色边框图像
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.image_processor import ImageProcessor
    print("OpenCV导入成功，可以运行完整示例")
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"OpenCV导入失败: {e}")
    print("将运行模拟示例")
    OPENCV_AVAILABLE = False


def create_sample_image():
    """创建示例A4纸黑色边框图像"""
    if not OPENCV_AVAILABLE:
        print("创建模拟图像数据...")
        return "模拟图像数据"
    
    print("创建示例A4纸图像...")
    
    # A4纸比例 (210x297mm)，使用800x600像素
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 2cm边框 (假设28.35像素/cm)
    border_width = int(2 * 28.35)  # 约57像素
    
    # 绘制黑色边框
    cv2.rectangle(image, (0, 0), (width-1, border_width-1), (0, 0, 0), -1)  # 上
    cv2.rectangle(image, (0, height-border_width), (width-1, height-1), (0, 0, 0), -1)  # 下
    cv2.rectangle(image, (0, 0), (border_width-1, height-1), (0, 0, 0), -1)  # 左
    cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), (0, 0, 0), -1)  # 右
    
    # 添加一些文字内容（模拟文档）
    cv2.putText(image, "Sample Document", (150, 200), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "A4 Paper with 2cm Border", (120, 250), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    # 添加轻微噪声
    noise = np.random.normal(0, 10, image.shape).astype(np.int16)
    image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    return image


def demonstrate_preprocessing():
    """演示图像预处理过程"""
    print("\n=== 图像预处理演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟预处理过程:")
        print("1. 灰度转换: BGR -> 灰度")
        print("2. 高斯模糊: 去除噪声")
        print("3. 自适应二值化: 处理光照不均")
        print("4. 形态学操作: 开运算 + 闭运算")
        print("✓ 预处理完成（模拟）")
        return True
    
    # 创建示例图像
    sample_image = create_sample_image()
    
    # 创建图像处理器
    processor = ImageProcessor()
    
    try:
        print("开始预处理...")
        
        # 执行预处理
        processed_image = processor.preprocess(sample_image)
        
        print(f"✓ 原始图像尺寸: {sample_image.shape}")
        print(f"✓ 处理后图像尺寸: {processed_image.shape}")
        print(f"✓ 处理后图像类型: {processed_image.dtype}")
        
        # 分析二值化结果
        unique_values = np.unique(processed_image)
        print(f"✓ 二值化结果包含值: {unique_values}")
        
        # 计算边框像素比例
        white_pixels = np.sum(processed_image == 255)
        total_pixels = processed_image.size
        border_ratio = white_pixels / total_pixels
        print(f"✓ 边框像素比例: {border_ratio:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预处理失败: {e}")
        return False


def demonstrate_debug_features():
    """演示调试功能"""
    print("\n=== 调试功能演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟调试功能:")
        print("- 可以保存各处理步骤的中间结果")
        print("- 包括: 原图 -> 灰度 -> 模糊 -> 二值化 -> 形态学处理")
        print("- 便于分析和调优参数")
        return True
    
    sample_image = create_sample_image()
    processor = ImageProcessor()
    
    try:
        # 使用调试功能
        debug_results = processor.debug_show_steps(sample_image)
        
        print("调试信息:")
        for step_name, step_image in debug_results.items():
            if step_image is not None:
                print(f"✓ {step_name}: {step_image.shape} {step_image.dtype}")
            else:
                print(f"❌ {step_name}: None")
        
        print("✓ 调试功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 调试功能失败: {e}")
        return False


def demonstrate_parameter_effects():
    """演示参数调整效果"""
    print("\n=== 参数调整演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("参数调整说明:")
        print("- GAUSSIAN_BLUR_KERNEL: 控制模糊程度，影响噪声去除")
        print("- ADAPTIVE_THRESH_BLOCK_SIZE: 控制自适应阈值的局部区域大小")
        print("- ADAPTIVE_THRESH_C: 控制阈值的偏移量")
        print("- MORPH_KERNEL_SIZE: 控制形态学操作的强度")
        return True
    
    sample_image = create_sample_image()
    
    # 测试不同参数
    print("测试不同的高斯模糊核大小:")
    kernel_sizes = [(3, 3), (5, 5), (7, 7)]
    
    for kernel_size in kernel_sizes:
        try:
            # 临时修改参数
            processor = ImageProcessor()
            processor.gaussian_kernel = kernel_size
            
            result = processor.preprocess(sample_image)
            noise_level = np.std(result.astype(np.float32))
            
            print(f"  核大小 {kernel_size}: 噪声水平 {noise_level:.2f}")
            
        except Exception as e:
            print(f"  核大小 {kernel_size}: 处理失败 - {e}")
    
    return True


def main():
    """主演示函数"""
    print("=== 图像预处理模块使用示例 ===")
    
    if OPENCV_AVAILABLE:
        print("✓ OpenCV环境可用，运行完整演示")
    else:
        print("⚠ OpenCV环境不可用，运行模拟演示")
    
    # 执行各项演示
    demos = [
        ("基础预处理", demonstrate_preprocessing),
        ("调试功能", demonstrate_debug_features),
        ("参数调整", demonstrate_parameter_effects)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        print(f"\n--- {demo_name} ---")
        try:
            result = demo_func()
            results.append(result)
            if result:
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {e}")
            results.append(False)
    
    # 总结
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== 演示总结 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有演示都成功完成！")
        print("\n📖 使用说明:")
        print("1. 创建 ImageProcessor 实例")
        print("2. 调用 preprocess(image) 方法处理图像")
        print("3. 使用 debug_show_steps() 进行调试")
        print("4. 根据需要调整 config.py 中的参数")
    else:
        print("⚠ 部分演示未成功，请检查环境配置")
    
    return 0 if success_count == total_count else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
