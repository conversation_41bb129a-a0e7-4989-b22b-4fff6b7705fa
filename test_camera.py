#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄像头功能测试
"""

import sys
import time

try:
    import cv2
    import numpy as np
    print("✓ OpenCV导入成功")
except ImportError as e:
    print(f"❌ OpenCV导入失败: {e}")
    sys.exit(1)


def test_camera_basic():
    """测试基本摄像头功能"""
    print("=== 测试摄像头基本功能 ===")
    
    try:
        # 尝试打开摄像头
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ 无法打开摄像头")
            return False
        
        print("✓ 摄像头打开成功")
        
        # 获取摄像头信息
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        
        print(f"摄像头参数: {width}x{height} @ {fps}FPS")
        
        # 测试读取帧
        ret, frame = cap.read()
        if ret:
            print(f"✓ 成功读取帧: {frame.shape}")
        else:
            print("❌ 无法读取帧")
            cap.release()
            return False
        
        cap.release()
        print("✓ 摄像头基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 摄像头测试失败: {e}")
        return False


def test_camera_preview():
    """测试摄像头预览"""
    print("\n=== 摄像头预览测试 ===")
    print("将显示摄像头画面，按 'q' 退出")
    
    try:
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ 无法打开摄像头")
            return False
        
        # 设置窗口
        window_name = "Camera Preview - Press 'q' to quit"
        cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
        
        frame_count = 0
        start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取帧")
                break
            
            frame_count += 1
            
            # 添加信息文字
            cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 计算FPS
            elapsed_time = time.time() - start_time
            if elapsed_time > 0:
                fps = frame_count / elapsed_time
                cv2.putText(frame, f"FPS: {fps:.1f}", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            cv2.putText(frame, "Press 'q' to quit", (10, frame.shape[0] - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 显示帧
            cv2.imshow(window_name, frame)
            
            # 检查按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"✓ 预览测试完成，共处理 {frame_count} 帧")
        return True
        
    except Exception as e:
        print(f"❌ 预览测试失败: {e}")
        return False


def create_test_border_image():
    """创建测试边框图像"""
    print("\n=== 创建测试边框图像 ===")
    
    try:
        # 创建A4纸测试图像
        height, width = 600, 800
        image = np.ones((height, width, 3), dtype=np.uint8) * 255  # 白色背景
        
        # 添加2cm黑色边框
        border_width = int(2 * 28.35)  # 约57像素
        cv2.rectangle(image, (0, 0), (width-1, border_width-1), (0, 0, 0), -1)  # 上
        cv2.rectangle(image, (0, height-border_width), (width-1, height-1), (0, 0, 0), -1)  # 下
        cv2.rectangle(image, (0, 0), (border_width-1, height-1), (0, 0, 0), -1)  # 左
        cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), (0, 0, 0), -1)  # 右
        
        # 添加文字
        cv2.putText(image, "Test A4 Border", (200, 300), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 3)
        cv2.putText(image, "Hold this in front of camera", (150, 350), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        
        # 保存图像
        filename = "test_border_image.jpg"
        cv2.imwrite(filename, image)
        print(f"✓ 测试图像已保存: {filename}")
        
        # 显示图像
        cv2.imshow("Test Border Image - Press any key to close", image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试图像失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=== 摄像头功能测试 ===")
    
    tests = [
        ("摄像头基本功能", test_camera_basic),
        ("创建测试图像", create_test_border_image),
        ("摄像头预览", test_camera_preview)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append(result)
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append(False)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("✅ 所有摄像头测试通过！")
        print("\n📖 使用说明:")
        print("1. 运行边框检测: python camera_border_detection.py")
        print("2. 将打印的A4纸边框图像放在摄像头前")
        print("3. 系统会实时检测并用绿色/红色线条标出内外边框")
        return 0
    else:
        print("❌ 部分测试失败，请检查摄像头连接")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
