#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果可视化与调试模块
实现检测结果的可视化显示，在原图上绘制内外边界框，提供调试信息输出
"""

import sys
import os
import time
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from config import *
except ImportError as e:
    print(f"导入依赖失败: {e}")
    print("请确保已安装OpenCV和NumPy")
    sys.exit(1)


class Visualizer:
    """可视化类，用于显示边框检测结果和调试信息"""
    
    def __init__(self):
        """初始化可视化器"""
        self.outer_color = OUTER_BORDER_COLOR  # 外边界颜色(绿色)
        self.inner_color = INNER_BORDER_COLOR  # 内边界颜色(红色)
        self.thickness = BORDER_THICKNESS  # 边界线宽度
        
        # 调试信息配置
        self.font = cv2.FONT_HERSHEY_SIMPLEX  # 字体
        self.font_scale = 0.6  # 字体大小
        self.text_color = (255, 255, 255)  # 文字颜色(白色)
        self.text_thickness = 2  # 文字线宽
    
    def draw_boundaries(self, image, outer_rect, inner_rect):
        """
        在图像上绘制内外边界框
        Args:
            image: 原始图像
            outer_rect: 外边界矩形 (x, y, w, h)
            inner_rect: 内边界矩形 (x, y, w, h)
        Returns:
            result: 绘制了边界框的图像
        """
        if image is None:
            raise ValueError("输入图像不能为空")
        
        if outer_rect is None or inner_rect is None:
            raise ValueError("边界矩形不能为空")
        
        # 复制图像避免修改原图
        result = image.copy()
        
        # 绘制外边界框(绿色)
        x1, y1, w1, h1 = outer_rect
        cv2.rectangle(result, (x1, y1), (x1 + w1, y1 + h1), self.outer_color, self.thickness)
        
        # 绘制内边界框(红色)
        x2, y2, w2, h2 = inner_rect
        cv2.rectangle(result, (x2, y2), (x2 + w2, y2 + h2), self.inner_color, self.thickness)
        
        return result
    
    def draw_precise_boundaries(self, image, outer_vertices, inner_vertices):
        """
        绘制精确的边界顶点和连线
        Args:
            image: 原始图像
            outer_vertices: 外边界顶点列表 [[x1,y1], [x2,y2], ...]
            inner_vertices: 内边界顶点列表 [[x1,y1], [x2,y2], ...]
        Returns:
            result: 绘制了精确边界的图像
        """
        if image is None:
            raise ValueError("输入图像不能为空")
        
        result = image.copy()
        
        # 转换为numpy数组
        if isinstance(outer_vertices, list):
            outer_vertices = np.array(outer_vertices, dtype=np.int32)
        if isinstance(inner_vertices, list):
            inner_vertices = np.array(inner_vertices, dtype=np.int32)
        
        # 绘制外边界多边形
        cv2.polylines(result, [outer_vertices], True, self.outer_color, self.thickness)
        
        # 绘制内边界多边形
        cv2.polylines(result, [inner_vertices], True, self.inner_color, self.thickness)
        
        # 标注顶点
        corner_labels = ['1', '2', '3', '4']  # 左上、右上、右下、左下
        
        # 标注外边界顶点
        for i, vertex in enumerate(outer_vertices):
            cv2.circle(result, tuple(vertex), 5, self.outer_color, -1)
            cv2.putText(result, f'O{corner_labels[i]}', (vertex[0] + 8, vertex[1] - 8),
                       self.font, self.font_scale, self.outer_color, self.text_thickness)
        
        # 标注内边界顶点
        for i, vertex in enumerate(inner_vertices):
            cv2.circle(result, tuple(vertex), 4, self.inner_color, -1)
            cv2.putText(result, f'I{corner_labels[i]}', (vertex[0] + 8, vertex[1] + 15),
                       self.font, self.font_scale, self.inner_color, self.text_thickness)
        
        return result
    
    def add_info_overlay(self, image, info_dict):
        """
        在图像上添加信息覆盖层
        Args:
            image: 输入图像
            info_dict: 信息字典，包含要显示的信息
        Returns:
            result: 添加了信息覆盖层的图像
        """
        result = image.copy()
        
        # 创建半透明背景
        overlay = result.copy()
        info_height = len(info_dict) * 25 + 20  # 根据信息条数计算高度
        cv2.rectangle(overlay, (10, 10), (400, info_height), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, result, 0.3, 0, result)
        
        # 添加文字信息
        y_offset = 30
        for key, value in info_dict.items():
            text = f"{key}: {value}"
            cv2.putText(result, text, (20, y_offset), self.font, self.font_scale,
                       self.text_color, self.text_thickness)
            y_offset += 25
        
        return result
    
    def save_result(self, image, filename):
        """
        保存结果图像
        Args:
            image: 要保存的图像
            filename: 保存文件名
        Returns:
            success: 是否保存成功
        """
        try:
            # 确保目录存在
            directory = os.path.dirname(filename)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
            
            # 保存图像
            success = cv2.imwrite(filename, image)
            
            if success:
                print(f"✓ 结果图像已保存: {filename}")
                return True
            else:
                print(f"❌ 保存图像失败: {filename}")
                return False
                
        except Exception as e:
            print(f"❌ 保存图像异常: {e}")
            return False
    
    def display_info(self, outer_rect, inner_rect):
        """
        显示边界信息
        Args:
            outer_rect: 外边界矩形
            inner_rect: 内边界矩形
        """
        print("=== 边界检测结果 ===")
        print(f"外边界: {outer_rect}")
        print(f"  - 位置: ({outer_rect[0]}, {outer_rect[1]})")
        print(f"  - 尺寸: {outer_rect[2]} x {outer_rect[3]} 像素")
        
        print(f"内边界: {inner_rect}")
        print(f"  - 位置: ({inner_rect[0]}, {inner_rect[1]})")
        print(f"  - 尺寸: {inner_rect[2]} x {inner_rect[3]} 像素")
        
        # 计算边框宽度
        border_width_x = inner_rect[0] - outer_rect[0]
        border_width_y = inner_rect[1] - outer_rect[1]
        print(f"边框宽度: X方向={border_width_x}像素, Y方向={border_width_y}像素")
    
    def display_detailed_info(self, boundary_info):
        """
        显示详细的边界信息
        Args:
            boundary_info: 来自CoordinateCalculator的详细边界信息
        """
        print("=== 详细边界分析 ===")
        
        # 边界信息
        boundaries = boundary_info['boundaries']
        print("边界坐标:")
        print(f"  外边界矩形: {boundaries['outer_rect']}")
        print(f"  内边界矩形: {boundaries['inner_rect']}")
        
        # 面积信息
        areas = boundary_info['areas']
        print("面积信息:")
        print(f"  外边界面积: {areas['outer_pixels']:.0f}像素² = {areas['outer_cm2']:.2f}cm²")
        print(f"  内边界面积: {areas['inner_pixels']:.0f}像素² = {areas['inner_cm2']:.2f}cm²")
        print(f"  边框面积: {areas['border_pixels']:.0f}像素² = {areas['border_cm2']:.2f}cm²")
        
        # 尺寸信息
        dimensions = boundary_info['dimensions']
        print("尺寸信息:")
        print(f"  外边界: {dimensions['outer_width_pixels']}x{dimensions['outer_height_pixels']}像素")
        print(f"          = {dimensions['outer_width_cm']:.1f}x{dimensions['outer_height_cm']:.1f}厘米")
        print(f"  内边界: {dimensions['inner_width_pixels']}x{dimensions['inner_height_pixels']}像素")
        print(f"          = {dimensions['inner_width_cm']:.1f}x{dimensions['inner_height_cm']:.1f}厘米")
        
        # 系统设置
        settings = boundary_info['settings']
        print("系统设置:")
        print(f"  像素密度: {settings['pixels_per_cm']:.2f}像素/厘米")
        print(f"  边框宽度: {settings['border_width_cm']}厘米 = {settings['border_width_pixels']}像素")
    
    def create_comparison_view(self, original, processed, result):
        """
        创建对比视图，显示原图、处理后图像和结果
        Args:
            original: 原始图像
            processed: 处理后的图像
            result: 检测结果图像
        Returns:
            comparison: 对比视图图像
        """
        if original is None or processed is None or result is None:
            raise ValueError("输入图像不能为空")
        
        # 调整图像尺寸使其一致
        height = min(original.shape[0], processed.shape[0], result.shape[0])
        width = min(original.shape[1], processed.shape[1], result.shape[1])
        
        # 调整尺寸
        original_resized = cv2.resize(original, (width, height))
        if len(processed.shape) == 2:
            processed_resized = cv2.resize(processed, (width, height))
            processed_resized = cv2.cvtColor(processed_resized, cv2.COLOR_GRAY2BGR)
        else:
            processed_resized = cv2.resize(processed, (width, height))
        result_resized = cv2.resize(result, (width, height))
        
        # 水平拼接
        comparison = np.hstack([original_resized, processed_resized, result_resized])
        
        # 添加标题
        titles = ['Original', 'Processed', 'Result']
        title_positions = [width//4, width + width//4, 2*width + width//4]
        
        for i, (title, x_pos) in enumerate(zip(titles, title_positions)):
            cv2.putText(comparison, title, (x_pos - 40, 30), self.font, 0.8,
                       (255, 255, 255), 2)
        
        return comparison
    
    def save_debug_images(self, debug_data, base_filename):
        """
        保存调试图像序列
        Args:
            debug_data: 调试数据字典
            base_filename: 基础文件名
        Returns:
            saved_files: 保存的文件列表
        """
        saved_files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for step_name, image_data in debug_data.items():
            if image_data is not None:
                filename = f"{base_filename}_{timestamp}_{step_name}.jpg"
                
                # 确保是BGR格式用于保存
                if len(image_data.shape) == 2:
                    save_image = cv2.cvtColor(image_data, cv2.COLOR_GRAY2BGR)
                else:
                    save_image = image_data
                
                if self.save_result(save_image, filename):
                    saved_files.append(filename)
        
        return saved_files


def test_visualizer():
    """测试可视化器"""
    print("=== 可视化模块测试 ===")
    
    # 创建测试图像
    test_image = np.ones((400, 600, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 添加一些内容
    cv2.rectangle(test_image, (50, 50), (550, 350), (200, 200, 200), -1)  # 灰色矩形
    cv2.putText(test_image, "Test Image", (200, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # 定义测试边界
    outer_rect = (50, 50, 500, 300)
    inner_rect = (100, 100, 400, 200)
    
    # 创建可视化器并测试
    visualizer = Visualizer()
    
    try:
        # 测试边界绘制
        result = visualizer.draw_boundaries(test_image, outer_rect, inner_rect)
        print("✓ 边界绘制功能正常")
        
        # 测试信息显示
        visualizer.display_info(outer_rect, inner_rect)
        print("✓ 信息显示功能正常")
        
        # 测试图像保存
        test_filename = "examples/test_result.jpg"
        success = visualizer.save_result(result, test_filename)
        if success:
            print("✓ 图像保存功能正常")
        else:
            print("⚠ 图像保存功能异常（可能是目录权限问题）")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化模块测试失败: {e}")
        return False


if __name__ == "__main__":
    test_visualizer()
