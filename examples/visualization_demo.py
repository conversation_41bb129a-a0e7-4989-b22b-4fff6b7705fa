#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块使用示例
演示如何使用Visualizer类显示边框检测结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.visualizer import Visualizer
    from src.coordinate_calculator import CoordinateCalculator
    print("OpenCV导入成功，可以运行完整示例")
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"OpenCV导入失败: {e}")
    print("将运行模拟示例")
    OPENCV_AVAILABLE = False


def create_sample_document():
    """创建示例文档图像"""
    if not OPENCV_AVAILABLE:
        print("创建模拟文档图像数据...")
        return "模拟图像数据"
    
    print("创建示例A4文档图像...")
    
    # 创建A4纸图像 (800x600像素)
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 添加2cm黑色边框
    border_width = int(2 * 28.35)  # 约57像素
    cv2.rectangle(image, (0, 0), (width-1, border_width-1), (0, 0, 0), -1)  # 上
    cv2.rectangle(image, (0, height-border_width), (width-1, height-1), (0, 0, 0), -1)  # 下
    cv2.rectangle(image, (0, 0), (border_width-1, height-1), (0, 0, 0), -1)  # 左
    cv2.rectangle(image, (width-border_width, 0), (width-1, height-1), (0, 0, 0), -1)  # 右
    
    # 添加文档内容
    cv2.putText(image, "Sample A4 Document", (150, 150), 
                cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
    cv2.putText(image, "Border Detection Visualization", (100, 200), 
                cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
    cv2.putText(image, "Inner Content Area", (250, 300), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (100, 100, 100), 2)
    
    # 添加一些装饰元素
    cv2.rectangle(image, (150, 350), (650, 450), (200, 200, 200), 2)
    cv2.putText(image, "Text Block Example", (200, 400), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
    
    return image


def demonstrate_basic_visualization():
    """演示基本可视化功能"""
    print("\n=== 基本可视化演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟基本可视化:")
        print("1. 在原图上绘制绿色外边界框")
        print("2. 在原图上绘制红色内边界框")
        print("3. 保存标注后的结果图像")
        print("✓ 基本可视化演示完成（模拟）")
        return True
    
    try:
        # 创建示例图像
        sample_image = create_sample_document()
        
        # 定义边界（模拟检测结果）
        border_width = int(2 * 28.35)
        outer_rect = (0, 0, 800, 600)
        inner_rect = (border_width, border_width, 800-2*border_width, 600-2*border_width)
        
        # 创建可视化器
        visualizer = Visualizer()
        
        # 绘制边界
        result = visualizer.draw_boundaries(sample_image, outer_rect, inner_rect)
        
        # 显示信息
        visualizer.display_info(outer_rect, inner_rect)
        
        # 保存结果
        save_path = "examples/basic_visualization_result.jpg"
        success = visualizer.save_result(result, save_path)
        
        if success:
            print("✓ 基本可视化演示成功")
            print(f"  - 结果已保存到: {save_path}")
        else:
            print("⚠ 图像保存失败，但可视化功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本可视化演示失败: {e}")
        return False


def demonstrate_precise_visualization():
    """演示精确可视化功能"""
    print("\n=== 精确可视化演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟精确可视化:")
        print("1. 绘制精确的边界多边形")
        print("2. 标注各个顶点位置")
        print("3. 显示顶点编号和类型")
        print("✓ 精确可视化演示完成（模拟）")
        return True
    
    try:
        sample_image = create_sample_document()
        
        # 定义精确顶点
        border_width = int(2 * 28.35)
        outer_vertices = [
            [0, 0], [800, 0], [800, 600], [0, 600]
        ]
        inner_vertices = [
            [border_width, border_width], 
            [800-border_width, border_width],
            [800-border_width, 600-border_width], 
            [border_width, 600-border_width]
        ]
        
        visualizer = Visualizer()
        
        # 绘制精确边界
        result = visualizer.draw_precise_boundaries(sample_image, outer_vertices, inner_vertices)
        
        # 保存结果
        save_path = "examples/precise_visualization_result.jpg"
        success = visualizer.save_result(result, save_path)
        
        print("✓ 精确可视化演示成功")
        print(f"  - 外边界顶点: {len(outer_vertices)}个")
        print(f"  - 内边界顶点: {len(inner_vertices)}个")
        if success:
            print(f"  - 结果已保存到: {save_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 精确可视化演示失败: {e}")
        return False


def demonstrate_info_overlay():
    """演示信息覆盖层功能"""
    print("\n=== 信息覆盖层演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟信息覆盖层:")
        print("1. 创建半透明信息背景")
        print("2. 添加检测结果文字信息")
        print("3. 显示边界坐标和尺寸")
        print("✓ 信息覆盖层演示完成（模拟）")
        return True
    
    try:
        sample_image = create_sample_document()
        
        # 准备信息字典
        border_width = int(2 * 28.35)
        info_dict = {
            "检测状态": "成功",
            "外边界": f"(0, 0, 800, 600)",
            "内边界": f"({border_width}, {border_width}, {800-2*border_width}, {600-2*border_width})",
            "边框宽度": f"{border_width}像素 ≈ 2.0cm",
            "像素密度": "28.35像素/cm (72DPI)",
            "处理时间": "0.15秒"
        }
        
        visualizer = Visualizer()
        
        # 添加信息覆盖层
        result = visualizer.add_info_overlay(sample_image, info_dict)
        
        # 保存结果
        save_path = "examples/info_overlay_result.jpg"
        success = visualizer.save_result(result, save_path)
        
        print("✓ 信息覆盖层演示成功")
        print(f"  - 显示信息项: {len(info_dict)}个")
        if success:
            print(f"  - 结果已保存到: {save_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 信息覆盖层演示失败: {e}")
        return False


def demonstrate_comparison_view():
    """演示对比视图功能"""
    print("\n=== 对比视图演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟对比视图:")
        print("1. 水平拼接原图、处理图、结果图")
        print("2. 添加图像标题")
        print("3. 统一图像尺寸")
        print("✓ 对比视图演示完成（模拟）")
        return True
    
    try:
        # 创建三个阶段的图像
        original = create_sample_document()
        
        # 模拟处理后的图像（灰度+二值化效果）
        processed = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
        processed = cv2.threshold(processed, 127, 255, cv2.THRESH_BINARY)[1]
        
        # 模拟检测结果图像
        result = original.copy()
        border_width = int(2 * 28.35)
        outer_rect = (0, 0, 800, 600)
        inner_rect = (border_width, border_width, 800-2*border_width, 600-2*border_width)
        
        visualizer = Visualizer()
        result = visualizer.draw_boundaries(result, outer_rect, inner_rect)
        
        # 创建对比视图
        comparison = visualizer.create_comparison_view(original, processed, result)
        
        # 保存对比视图
        save_path = "examples/comparison_view_result.jpg"
        success = visualizer.save_result(comparison, save_path)
        
        print("✓ 对比视图演示成功")
        print(f"  - 对比视图尺寸: {comparison.shape}")
        print(f"  - 包含图像: 原图 + 处理图 + 结果图")
        if success:
            print(f"  - 结果已保存到: {save_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比视图演示失败: {e}")
        return False


def demonstrate_detailed_info():
    """演示详细信息显示"""
    print("\n=== 详细信息显示演示 ===")
    
    if not OPENCV_AVAILABLE:
        print("模拟详细信息显示:")
        print("- 边界坐标和顶点信息")
        print("- 面积计算（像素²和cm²）")
        print("- 尺寸信息（像素和厘米）")
        print("- 系统设置参数")
        print("✓ 详细信息显示演示完成（模拟）")
        return True
    
    try:
        # 模拟详细边界信息（来自CoordinateCalculator）
        boundary_info = {
            'boundaries': {
                'outer_rect': (0, 0, 800, 600),
                'inner_rect': (57, 57, 686, 486),
                'outer_vertices': [[0, 0], [800, 0], [800, 600], [0, 600]],
                'inner_vertices': [[57, 57], [743, 57], [743, 543], [57, 543]]
            },
            'areas': {
                'outer_pixels': 480000,
                'outer_cm2': 596.8,
                'inner_pixels': 333396,
                'inner_cm2': 414.7,
                'border_pixels': 146604,
                'border_cm2': 182.1
            },
            'dimensions': {
                'outer_width_pixels': 800,
                'outer_height_pixels': 600,
                'outer_width_cm': 28.2,
                'outer_height_cm': 21.2,
                'inner_width_pixels': 686,
                'inner_height_pixels': 486,
                'inner_width_cm': 24.2,
                'inner_height_cm': 17.1
            },
            'settings': {
                'pixels_per_cm': 28.35,
                'border_width_cm': 2.0,
                'border_width_pixels': 57
            }
        }
        
        visualizer = Visualizer()
        
        # 显示详细信息
        visualizer.display_detailed_info(boundary_info)
        
        print("✓ 详细信息显示演示成功")
        return True
        
    except Exception as e:
        print(f"❌ 详细信息显示演示失败: {e}")
        return False


def main():
    """主演示函数"""
    print("=== 可视化模块使用示例 ===")
    
    if OPENCV_AVAILABLE:
        print("✓ OpenCV环境可用，运行完整演示")
    else:
        print("⚠ OpenCV环境不可用，运行模拟演示")
    
    # 执行各项演示
    demos = [
        ("基本可视化", demonstrate_basic_visualization),
        ("精确可视化", demonstrate_precise_visualization),
        ("信息覆盖层", demonstrate_info_overlay),
        ("对比视图", demonstrate_comparison_view),
        ("详细信息显示", demonstrate_detailed_info)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        print(f"\n--- {demo_name} ---")
        try:
            result = demo_func()
            results.append(result)
            if result:
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {e}")
            results.append(False)
    
    # 总结
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== 演示总结 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有演示都成功完成！")
        print("\n📖 使用说明:")
        print("1. 创建 Visualizer 实例")
        print("2. 使用 draw_boundaries() 绘制基本边界框")
        print("3. 使用 draw_precise_boundaries() 绘制精确顶点")
        print("4. 使用 add_info_overlay() 添加信息覆盖层")
        print("5. 使用 create_comparison_view() 创建对比视图")
        print("6. 使用 save_result() 保存可视化结果")
        print("7. 使用 display_info() 和 display_detailed_info() 显示检测信息")
        
        if OPENCV_AVAILABLE:
            print("\n📁 生成的示例文件:")
            example_files = [
                "examples/basic_visualization_result.jpg",
                "examples/precise_visualization_result.jpg", 
                "examples/info_overlay_result.jpg",
                "examples/comparison_view_result.jpg"
            ]
            for file_path in example_files:
                if os.path.exists(file_path):
                    print(f"  ✓ {file_path}")
                else:
                    print(f"  - {file_path} (未生成)")
    else:
        print("⚠ 部分演示未成功，请检查环境配置")
    
    return 0 if success_count == total_count else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
