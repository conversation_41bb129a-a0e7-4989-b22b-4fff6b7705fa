#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块结构测试（不依赖OpenCV）
"""

import sys
import os
import ast

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_file_structure():
    """测试文件结构"""
    print("1. 测试文件结构...")
    
    # 检查必要文件是否存在
    required_files = [
        'src/visualizer.py',
        'tests/test_visualizer.py',
        'config.py'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            return False
    
    return True


def test_code_syntax():
    """测试代码语法"""
    print("2. 测试代码语法...")
    
    files_to_check = [
        'src/visualizer.py',
        'tests/test_visualizer.py'
    ]
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            # 解析语法
            ast.parse(code)
            print(f"✓ {file_path} 语法正确")
            
        except SyntaxError as e:
            print(f"❌ {file_path} 语法错误: {e}")
            return False
        except Exception as e:
            print(f"❌ {file_path} 检查失败: {e}")
            return False
    
    return True


def test_class_structure():
    """测试Visualizer类结构"""
    print("3. 测试Visualizer类结构...")
    
    try:
        # 读取源文件
        with open('src/visualizer.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 解析AST
        tree = ast.parse(code)
        
        # 查找Visualizer类
        visualizer_class = None
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == 'Visualizer':
                visualizer_class = node
                break
        
        if not visualizer_class:
            print("❌ 未找到Visualizer类")
            return False
        
        print("✓ Visualizer类存在")
        
        # 检查必要方法
        required_methods = [
            '__init__',
            'draw_boundaries',
            'draw_precise_boundaries',
            'add_info_overlay',
            'save_result',
            'display_info',
            'display_detailed_info',
            'create_comparison_view'
        ]
        
        found_methods = []
        for node in visualizer_class.body:
            if isinstance(node, ast.FunctionDef):
                found_methods.append(node.name)
        
        for method in required_methods:
            if method in found_methods:
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 类结构测试失败: {e}")
        return False


def test_config_integration():
    """测试配置集成"""
    print("4. 测试配置集成...")
    
    try:
        # 读取可视化模块源文件
        with open('src/visualizer.py', 'r', encoding='utf-8') as f:
            visualizer_code = f.read()
        
        # 读取配置文件
        with open('config.py', 'r', encoding='utf-8') as f:
            config_code = f.read()
        
        # 检查可视化相关配置参数
        required_config_params = [
            'OUTER_BORDER_COLOR',
            'INNER_BORDER_COLOR',
            'BORDER_THICKNESS'
        ]
        
        for param in required_config_params:
            if param in config_code:
                print(f"✓ 配置参数 {param} 存在")
            else:
                print(f"❌ 配置参数 {param} 缺失")
                return False
        
        # 检查可视化模块是否使用了这些配置
        config_usage = [
            'OUTER_BORDER_COLOR',
            'INNER_BORDER_COLOR',
            'BORDER_THICKNESS'
        ]
        
        for usage in config_usage:
            if usage in visualizer_code:
                print(f"✓ 使用配置参数 {usage}")
            else:
                print(f"❌ 未使用配置参数 {usage}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置集成测试失败: {e}")
        return False


def test_method_signatures():
    """测试方法签名"""
    print("5. 测试方法签名...")
    
    try:
        with open('src/visualizer.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        tree = ast.parse(code)
        
        # 查找Visualizer类
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == 'Visualizer':
                # 检查关键方法的签名
                method_signatures = {
                    'draw_boundaries': ['self', 'image', 'outer_rect', 'inner_rect'],
                    'save_result': ['self', 'image', 'filename'],
                    'display_info': ['self', 'outer_rect', 'inner_rect']
                }
                
                for method_node in node.body:
                    if isinstance(method_node, ast.FunctionDef):
                        method_name = method_node.name
                        if method_name in method_signatures:
                            args = [arg.arg for arg in method_node.args.args]
                            expected_args = method_signatures[method_name]
                            if args == expected_args:
                                print(f"✓ {method_name}方法签名正确")
                            else:
                                print(f"❌ {method_name}方法签名错误: {args} (期望: {expected_args})")
                                return False
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 方法签名测试失败: {e}")
        return False


def test_documentation():
    """测试文档字符串"""
    print("6. 测试文档字符串...")
    
    try:
        with open('src/visualizer.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        tree = ast.parse(code)
        
        # 检查模块文档字符串
        if ast.get_docstring(tree):
            print("✓ 模块文档字符串存在")
        else:
            print("❌ 模块文档字符串缺失")
            return False
        
        # 检查类文档字符串
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == 'Visualizer':
                if ast.get_docstring(node):
                    print("✓ Visualizer类文档字符串存在")
                else:
                    print("❌ Visualizer类文档字符串缺失")
                    return False
                
                # 检查主要方法的文档字符串
                key_methods = ['draw_boundaries', 'save_result', 'display_info']
                for method_node in node.body:
                    if isinstance(method_node, ast.FunctionDef) and method_node.name in key_methods:
                        if ast.get_docstring(method_node):
                            print(f"✓ {method_node.name}方法文档字符串存在")
                        else:
                            print(f"❌ {method_node.name}方法文档字符串缺失")
                            return False
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 文档字符串测试失败: {e}")
        return False


def test_opencv_functions():
    """测试OpenCV函数使用"""
    print("7. 测试OpenCV函数使用...")
    
    try:
        with open('src/visualizer.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 检查关键OpenCV函数是否存在
        opencv_functions = [
            'cv2.rectangle',      # 绘制矩形
            'cv2.polylines',      # 绘制多边形
            'cv2.circle',         # 绘制圆形
            'cv2.putText',        # 添加文字
            'cv2.imwrite',        # 保存图像
            'cv2.addWeighted',    # 图像混合
            'cv2.resize'          # 图像缩放
        ]
        
        for func in opencv_functions:
            if func in code:
                print(f"✓ OpenCV函数 {func} 存在")
            else:
                print(f"❌ OpenCV函数 {func} 缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ OpenCV函数测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("8. 测试错误处理...")
    
    try:
        with open('src/visualizer.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 检查是否包含异常处理
        if 'try:' in code and 'except' in code:
            print("✓ 包含异常处理代码")
        else:
            print("❌ 缺少异常处理代码")
            return False
        
        # 检查是否有输入验证
        if 'ValueError' in code and ('is None' in code or 'None' in code):
            print("✓ 包含输入验证")
        else:
            print("❌ 缺少输入验证")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=== 可视化模块结构测试 ===")
    
    test_functions = [
        test_file_structure,
        test_code_syntax,
        test_class_structure,
        test_config_integration,
        test_method_signatures,
        test_documentation,
        test_opencv_functions,
        test_error_handling
    ]
    
    results = []
    for test_func in test_functions:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 执行失败: {e}")
            results.append(False)
        print()  # 空行分隔
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("✅ 所有结构测试通过！")
        print("📝 注意: 由于系统依赖问题，未执行OpenCV功能测试")
        print("📝 但代码结构完整，OpenCV函数齐全，符合设计要求")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
