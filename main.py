#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV边框检测系统主程序
用于识别A4纸上2cm宽黑色边框线的内外边界
"""

import cv2
import sys
import os

def verify_opencv_installation():
    """验证OpenCV安装"""
    try:
        print(f"OpenCV版本: {cv2.__version__}")
        print("OpenCV安装验证成功!")
        return True
    except Exception as e:
        print(f"OpenCV安装验证失败: {e}")
        return False

def main():
    """主程序入口"""
    print("=== OpenCV边框检测系统 ===")
    
    # 验证OpenCV安装
    if not verify_opencv_installation():
        sys.exit(1)
    
    # 检查项目结构
    required_dirs = ['src', 'tests', 'config', 'examples']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"警告: 缺少目录 {dir_name}")
        else:
            print(f"✓ 目录 {dir_name} 存在")
    
    print("项目初始化完成!")

if __name__ == "__main__":
    main()
