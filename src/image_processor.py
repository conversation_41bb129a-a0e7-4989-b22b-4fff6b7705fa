#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预处理模块
实现图像的灰度转换、噪声滤除、自适应二值化等预处理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from config import *
except ImportError as e:
    print(f"导入依赖失败: {e}")
    print("请确保已安装OpenCV和NumPy")
    sys.exit(1)


class ImageProcessor:
    """图像预处理类，处理不同光照条件下的图像"""
    
    def __init__(self):
        """初始化图像处理器"""
        self.gaussian_kernel = GAUSSIAN_BLUR_KERNEL  # 高斯模糊核大小
        self.adaptive_block_size = ADAPTIVE_THRESH_BLOCK_SIZE  # 自适应阈值块大小
        self.adaptive_c = ADAPTIVE_THRESH_C  # 自适应阈值常数
        self.morph_kernel_size = MORPH_KERNEL_SIZE  # 形态学操作核大小
        self.morph_kernel_type = MORPH_KERNEL_TYPE  # 形态学操作核类型
    
    def preprocess(self, image):
        """
        主要预处理流程
        Args:
            image: 输入的BGR彩色图像
        Returns:
            binary: 预处理后的二值化图像
        """
        if image is None:
            raise ValueError("输入图像为空")
        
        # 1. 灰度转换
        gray = self._convert_to_gray(image)
        
        # 2. 噪声滤除
        blurred = self._gaussian_blur(gray)
        
        # 3. 自适应二值化
        binary = self._adaptive_threshold(blurred)
        
        # 4. 形态学操作
        processed = self.morphological_operations(binary)
        
        return processed
    
    def _convert_to_gray(self, image):
        """
        将彩色图像转换为灰度图像
        Args:
            image: BGR彩色图像
        Returns:
            gray: 灰度图像
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()  # 已经是灰度图像
        return gray
    
    def _gaussian_blur(self, gray):
        """
        高斯模糊去噪
        Args:
            gray: 灰度图像
        Returns:
            blurred: 模糊后的图像
        """
        blurred = cv2.GaussianBlur(gray, self.gaussian_kernel, 0)
        return blurred
    
    def _adaptive_threshold(self, blurred):
        """
        自适应二值化，处理光照不均问题
        Args:
            blurred: 模糊后的灰度图像
        Returns:
            binary: 二值化图像
        """
        binary = cv2.adaptiveThreshold(
            blurred, 
            255, 
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY_INV,  # 反转：黑色边框变为白色
            self.adaptive_block_size, 
            self.adaptive_c
        )
        return binary
    
    def morphological_operations(self, binary):
        """
        形态学操作：开运算去除小噪点，闭运算连接断裂边缘
        Args:
            binary: 二值化图像
        Returns:
            processed: 形态学处理后的图像
        """
        # 创建结构元素
        kernel = cv2.getStructuringElement(self.morph_kernel_type, self.morph_kernel_size)
        
        # 开运算：先腐蚀后膨胀，去除小噪点
        opened = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 闭运算：先膨胀后腐蚀，连接断裂的边缘
        closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel)
        
        return closed
    
    def enhance_contrast(self, image):
        """
        增强图像对比度（可选功能）
        Args:
            image: 输入图像
        Returns:
            enhanced: 对比度增强后的图像
        """
        # 使用CLAHE（限制对比度自适应直方图均衡化）
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        if len(image.shape) == 3:
            # 彩色图像：转换到LAB空间，只对L通道进行处理
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            lab[:, :, 0] = clahe.apply(lab[:, :, 0])
            enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        else:
            # 灰度图像：直接处理
            enhanced = clahe.apply(image)
        return enhanced
    
    def debug_show_steps(self, image, save_path=None):
        """
        调试功能：显示预处理的各个步骤
        Args:
            image: 输入图像
            save_path: 保存路径（可选）
        """
        if image is None:
            print("输入图像为空，无法进行调试显示")
            return
        
        # 各步骤处理
        gray = self._convert_to_gray(image)
        blurred = self._gaussian_blur(gray)
        binary = self._adaptive_threshold(blurred)
        processed = self.morphological_operations(binary)
        
        # 如果提供保存路径，保存各步骤图像
        if save_path:
            cv2.imwrite(f"{save_path}_1_gray.jpg", gray)
            cv2.imwrite(f"{save_path}_2_blurred.jpg", blurred)
            cv2.imwrite(f"{save_path}_3_binary.jpg", binary)
            cv2.imwrite(f"{save_path}_4_processed.jpg", processed)
            print(f"调试图像已保存到: {save_path}_*.jpg")
        
        return {
            'original': image,
            'gray': gray,
            'blurred': blurred,
            'binary': binary,
            'processed': processed
        }


def test_image_processor():
    """测试图像预处理器"""
    print("=== 图像预处理模块测试 ===")
    
    # 创建测试图像（模拟A4纸黑色边框）
    test_image = np.ones((600, 800, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 添加黑色边框（2cm约56像素，假设72DPI）
    border_width = 56
    test_image[:border_width, :] = [0, 0, 0]  # 上边框
    test_image[-border_width:, :] = [0, 0, 0]  # 下边框
    test_image[:, :border_width] = [0, 0, 0]  # 左边框
    test_image[:, -border_width:] = [0, 0, 0]  # 右边框
    
    # 添加一些噪声
    noise = np.random.randint(0, 50, test_image.shape, dtype=np.uint8)
    test_image = cv2.add(test_image, noise)
    
    # 创建处理器并测试
    processor = ImageProcessor()
    try:
        processed = processor.preprocess(test_image)
        print("✓ 图像预处理成功")
        print(f"✓ 输入图像尺寸: {test_image.shape}")
        print(f"✓ 输出图像尺寸: {processed.shape}")
        print(f"✓ 输出图像类型: {processed.dtype}")
        
        # 检查二值化效果
        unique_values = np.unique(processed)
        print(f"✓ 二值化结果包含值: {unique_values}")
        
        return True
    except Exception as e:
        print(f"❌ 图像预处理失败: {e}")
        return False


if __name__ == "__main__":
    test_image_processor()
