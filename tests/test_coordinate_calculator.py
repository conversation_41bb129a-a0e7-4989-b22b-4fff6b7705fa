#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标计算模块测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import cv2
    import numpy as np
    from src.coordinate_calculator import CoordinateCalculator
    OPENCV_AVAILABLE = True
except ImportError as e:
    print(f"导入失败: {e}")
    print("跳过OpenCV相关测试")
    OPENCV_AVAILABLE = False


def create_test_contour():
    """创建测试轮廓"""
    if not OPENCV_AVAILABLE:
        return None
    
    # 创建一个矩形轮廓（模拟边框检测结果）
    # 外边界：50,50 到 750,550 (700x500像素)
    contour = np.array([
        [[50, 50]],    # 左上
        [[750, 50]],   # 右上
        [[750, 550]],  # 右下
        [[50, 550]]    # 左下
    ], dtype=np.int32)
    
    return contour


def test_basic_boundary_calculation():
    """测试基本边界计算"""
    print("1. 测试基本边界计算...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        test_contour = create_test_contour()
        calculator = CoordinateCalculator(pixels_per_cm=28.35)  # 72DPI
        
        # 计算边界
        outer_rect, inner_rect = calculator.calculate_boundaries(test_contour)
        
        # 验证外边界
        expected_outer = (50, 50, 700, 500)
        assert outer_rect == expected_outer, f"外边界错误: 期望{expected_outer}, 实际{outer_rect}"
        
        # 验证内边界
        border_pixels = int(2 * 28.35)  # 2cm对应的像素数
        expected_inner = (50 + border_pixels, 50 + border_pixels, 
                         700 - 2 * border_pixels, 500 - 2 * border_pixels)
        assert inner_rect == expected_inner, f"内边界错误: 期望{expected_inner}, 实际{inner_rect}"
        
        print("✓ 基本边界计算测试通过")
        print(f"  - 外边界: {outer_rect}")
        print(f"  - 内边界: {inner_rect}")
        print(f"  - 边框宽度: {border_pixels}像素")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本边界计算测试失败: {e}")
        return False


def test_precise_boundary_calculation():
    """测试精确边界计算"""
    print("2. 测试精确边界计算...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        test_contour = create_test_contour()
        calculator = CoordinateCalculator(pixels_per_cm=28.35)
        
        # 计算精确边界
        precise_info = calculator.calculate_precise_boundaries(test_contour)
        
        # 验证返回的数据结构
        required_keys = ['outer_vertices', 'inner_vertices', 'outer_rect', 'inner_rect',
                        'border_width_pixels', 'border_width_cm', 'pixels_per_cm']
        
        for key in required_keys:
            assert key in precise_info, f"缺少键: {key}"
        
        # 验证顶点数量
        assert len(precise_info['outer_vertices']) == 4, "外边界应该有4个顶点"
        assert len(precise_info['inner_vertices']) == 4, "内边界应该有4个顶点"
        
        # 验证内边界在外边界内部
        outer_vertices = np.array(precise_info['outer_vertices'])
        inner_vertices = np.array(precise_info['inner_vertices'])
        
        # 检查内边界的每个顶点都在外边界内部
        for inner_vertex in inner_vertices:
            # 简单检查：内边界顶点的x,y坐标应该在合理范围内
            assert 50 < inner_vertex[0] < 750, f"内边界x坐标超出范围: {inner_vertex[0]}"
            assert 50 < inner_vertex[1] < 550, f"内边界y坐标超出范围: {inner_vertex[1]}"
        
        print("✓ 精确边界计算测试通过")
        print(f"  - 外边界顶点数: {len(precise_info['outer_vertices'])}")
        print(f"  - 内边界顶点数: {len(precise_info['inner_vertices'])}")
        print(f"  - 边框宽度: {precise_info['border_width_pixels']}像素 = {precise_info['border_width_cm']}cm")
        
        return True
        
    except Exception as e:
        print(f"❌ 精确边界计算测试失败: {e}")
        return False


def test_unit_conversion():
    """测试单位转换"""
    print("3. 测试单位转换...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        calculator = CoordinateCalculator(pixels_per_cm=28.35)  # 72DPI
        
        # 测试像素转厘米
        test_cases = [
            (28.35, 1.0),    # 1cm
            (56.7, 2.0),     # 2cm
            (141.75, 5.0),   # 5cm
            (283.5, 10.0)    # 10cm
        ]
        
        for pixels, expected_cm in test_cases:
            actual_cm = calculator.pixel_to_cm(pixels)
            assert abs(actual_cm - expected_cm) < 0.01, f"像素转厘米错误: {pixels}px -> {actual_cm}cm (期望{expected_cm}cm)"
        
        # 测试厘米转像素
        for expected_pixels, cm in test_cases:
            actual_pixels = calculator.cm_to_pixel(cm)
            assert abs(actual_pixels - expected_pixels) < 1, f"厘米转像素错误: {cm}cm -> {actual_pixels}px (期望{expected_pixels}px)"
        
        print("✓ 单位转换测试通过")
        print(f"  - 像素密度: {calculator.pixels_per_cm:.2f} 像素/厘米")
        
        return True
        
    except Exception as e:
        print(f"❌ 单位转换测试失败: {e}")
        return False


def test_dpi_settings():
    """测试DPI设置"""
    print("4. 测试DPI设置...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        calculator = CoordinateCalculator()
        
        # 测试不同DPI设置
        dpi_tests = [
            (72, 28.35),
            (96, 37.8),
            (150, 59.06),
            (300, 118.11)
        ]
        
        for dpi, expected_ppcm in dpi_tests:
            calculator.set_dpi(dpi)
            actual_ppcm = calculator.pixels_per_cm
            assert abs(actual_ppcm - expected_ppcm) < 0.1, f"DPI设置错误: {dpi}DPI -> {actual_ppcm} (期望{expected_ppcm})"
            
            # 验证边框宽度也相应更新
            expected_border_pixels = int(2 * expected_ppcm)
            assert calculator.border_width_pixels == expected_border_pixels, "边框宽度像素值未正确更新"
        
        print("✓ DPI设置测试通过")
        for dpi, ppcm in dpi_tests:
            print(f"  - {dpi}DPI = {ppcm:.2f} 像素/厘米")
        
        return True
        
    except Exception as e:
        print(f"❌ DPI设置测试失败: {e}")
        return False


def test_boundary_info():
    """测试边界信息获取"""
    print("5. 测试边界信息获取...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    try:
        test_contour = create_test_contour()
        calculator = CoordinateCalculator(pixels_per_cm=28.35)
        
        # 获取详细边界信息
        info = calculator.get_boundary_info(test_contour)
        
        # 验证信息结构
        required_sections = ['boundaries', 'areas', 'dimensions', 'settings']
        for section in required_sections:
            assert section in info, f"缺少信息段: {section}"
        
        # 验证面积信息
        areas = info['areas']
        assert areas['outer_pixels'] > areas['inner_pixels'], "外边界面积应该大于内边界面积"
        assert areas['border_pixels'] > 0, "边框面积应该大于0"
        assert areas['outer_pixels'] == areas['inner_pixels'] + areas['border_pixels'], "面积计算不一致"
        
        # 验证尺寸信息
        dimensions = info['dimensions']
        assert dimensions['outer_width_pixels'] > dimensions['inner_width_pixels'], "外边界宽度应该大于内边界宽度"
        assert dimensions['outer_height_pixels'] > dimensions['inner_height_pixels'], "外边界高度应该大于内边界高度"
        
        print("✓ 边界信息获取测试通过")
        print(f"  - 外边界面积: {areas['outer_pixels']:.0f}像素² = {areas['outer_cm2']:.2f}cm²")
        print(f"  - 内边界面积: {areas['inner_pixels']:.0f}像素² = {areas['inner_cm2']:.2f}cm²")
        print(f"  - 边框面积: {areas['border_pixels']:.0f}像素² = {areas['border_cm2']:.2f}cm²")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界信息获取测试失败: {e}")
        return False


def test_edge_cases():
    """测试边界情况"""
    print("6. 测试边界情况...")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV不可用，跳过测试")
        return True
    
    calculator = CoordinateCalculator()
    
    try:
        # 测试空轮廓
        try:
            calculator.calculate_boundaries(None)
            assert False, "应该抛出异常"
        except ValueError:
            print("✓ 空轮廓异常处理正确")
        
        # 测试过小的轮廓（会导致内边界无效）
        tiny_contour = np.array([
            [[0, 0]], [[10, 0]], [[10, 10]], [[0, 10]]
        ], dtype=np.int32)
        
        try:
            calculator.calculate_boundaries(tiny_contour)
            # 如果没有抛出异常，检查结果是否合理
            print("⚠ 小轮廓处理：未抛出异常，但可能结果不合理")
        except ValueError:
            print("✓ 小轮廓异常处理正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=== 坐标计算模块测试 ===")
    
    if not OPENCV_AVAILABLE:
        print("⚠ OpenCV环境不可用，将跳过大部分测试")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_basic_boundary_calculation())
    test_results.append(test_precise_boundary_calculation())
    test_results.append(test_unit_conversion())
    test_results.append(test_dpi_settings())
    test_results.append(test_boundary_info())
    test_results.append(test_edge_cases())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("✅ 所有测试通过！")
        if not OPENCV_AVAILABLE:
            print("📝 注意: 由于OpenCV不可用，部分测试被跳过")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
